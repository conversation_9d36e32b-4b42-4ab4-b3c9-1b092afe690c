# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: collections.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ollections.proto\x12\x06qdrant\"\x83\x03\n\x0cVectorParams\x12\x0c\n\x04size\x18\x01 \x01(\x04\x12\"\n\x08\x64istance\x18\x02 \x01(\x0e\x32\x10.qdrant.Distance\x12\x30\n\x0bhnsw_config\x18\x03 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12<\n\x13quantization_config\x18\x04 \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\x01\x88\x01\x01\x12\x14\n\x07on_disk\x18\x05 \x01(\x08H\x02\x88\x01\x01\x12\'\n\x08\x64\x61tatype\x18\x06 \x01(\x0e\x32\x10.qdrant.DatatypeH\x03\x88\x01\x01\x12:\n\x12multivector_config\x18\x07 \x01(\x0b\x32\x19.qdrant.MultiVectorConfigH\x04\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\x16\n\x14_quantization_configB\n\n\x08_on_diskB\x0b\n\t_datatypeB\x15\n\x13_multivector_config\"\xd0\x01\n\x10VectorParamsDiff\x12\x30\n\x0bhnsw_config\x18\x01 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12@\n\x13quantization_config\x18\x02 \x01(\x0b\x32\x1e.qdrant.QuantizationConfigDiffH\x01\x88\x01\x01\x12\x14\n\x07on_disk\x18\x03 \x01(\x08H\x02\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\x16\n\x14_quantization_configB\n\n\x08_on_disk\"\x82\x01\n\x0fVectorParamsMap\x12-\n\x03map\x18\x01 \x03(\x0b\x32 .qdrant.VectorParamsMap.MapEntry\x1a@\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.qdrant.VectorParams:\x02\x38\x01\"\x8e\x01\n\x13VectorParamsDiffMap\x12\x31\n\x03map\x18\x01 \x03(\x0b\x32$.qdrant.VectorParamsDiffMap.MapEntry\x1a\x44\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.qdrant.VectorParamsDiff:\x02\x38\x01\"p\n\rVectorsConfig\x12&\n\x06params\x18\x01 \x01(\x0b\x32\x14.qdrant.VectorParamsH\x00\x12-\n\nparams_map\x18\x02 \x01(\x0b\x32\x17.qdrant.VectorParamsMapH\x00\x42\x08\n\x06\x63onfig\"|\n\x11VectorsConfigDiff\x12*\n\x06params\x18\x01 \x01(\x0b\x32\x18.qdrant.VectorParamsDiffH\x00\x12\x31\n\nparams_map\x18\x02 \x01(\x0b\x32\x1b.qdrant.VectorParamsDiffMapH\x00\x42\x08\n\x06\x63onfig\"\x83\x01\n\x12SparseVectorParams\x12-\n\x05index\x18\x01 \x01(\x0b\x32\x19.qdrant.SparseIndexConfigH\x00\x88\x01\x01\x12\'\n\x08modifier\x18\x02 \x01(\x0e\x32\x10.qdrant.ModifierH\x01\x88\x01\x01\x42\x08\n\x06_indexB\x0b\n\t_modifier\"\x8e\x01\n\x12SparseVectorConfig\x12\x30\n\x03map\x18\x01 \x03(\x0b\x32#.qdrant.SparseVectorConfig.MapEntry\x1a\x46\n\x08MapEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\x05value\x18\x02 \x01(\x0b\x32\x1a.qdrant.SparseVectorParams:\x02\x38\x01\"F\n\x11MultiVectorConfig\x12\x31\n\ncomparator\x18\x01 \x01(\x0e\x32\x1d.qdrant.MultiVectorComparator\"3\n\x18GetCollectionInfoRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"2\n\x17\x43ollectionExistsRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"\"\n\x10\x43ollectionExists\x12\x0e\n\x06\x65xists\x18\x01 \x01(\x08\"R\n\x18\x43ollectionExistsResponse\x12(\n\x06result\x18\x01 \x01(\x0b\x32\x18.qdrant.CollectionExists\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\x18\n\x16ListCollectionsRequest\"%\n\x15\x43ollectionDescription\x12\x0c\n\x04name\x18\x01 \x01(\t\"Q\n\x19GetCollectionInfoResponse\x12&\n\x06result\x18\x01 \x01(\x0b\x32\x16.qdrant.CollectionInfo\x12\x0c\n\x04time\x18\x02 \x01(\x01\"[\n\x17ListCollectionsResponse\x12\x32\n\x0b\x63ollections\x18\x01 \x03(\x0b\x32\x1d.qdrant.CollectionDescription\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\x84\x01\n\x16MaxOptimizationThreads\x12\x0f\n\x05value\x18\x01 \x01(\x04H\x00\x12\x39\n\x07setting\x18\x02 \x01(\x0e\x32&.qdrant.MaxOptimizationThreads.SettingH\x00\"\x13\n\x07Setting\x12\x08\n\x04\x41uto\x10\x00\x42\t\n\x07variant\",\n\x0fOptimizerStatus\x12\n\n\x02ok\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\"\x90\x02\n\x0eHnswConfigDiff\x12\x0e\n\x01m\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x19\n\x0c\x65\x66_construct\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12 \n\x13\x66ull_scan_threshold\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12!\n\x14max_indexing_threads\x18\x04 \x01(\x04H\x03\x88\x01\x01\x12\x14\n\x07on_disk\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x16\n\tpayload_m\x18\x06 \x01(\x04H\x05\x88\x01\x01\x42\x04\n\x02_mB\x0f\n\r_ef_constructB\x16\n\x14_full_scan_thresholdB\x17\n\x15_max_indexing_threadsB\n\n\x08_on_diskB\x0c\n\n_payload_m\"\xa5\x01\n\x11SparseIndexConfig\x12 \n\x13\x66ull_scan_threshold\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x14\n\x07on_disk\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\'\n\x08\x64\x61tatype\x18\x03 \x01(\x0e\x32\x10.qdrant.DatatypeH\x02\x88\x01\x01\x42\x16\n\x14_full_scan_thresholdB\n\n\x08_on_diskB\x0b\n\t_datatype\"y\n\rWalConfigDiff\x12\x1c\n\x0fwal_capacity_mb\x18\x01 \x01(\x04H\x00\x88\x01\x01\x12\x1f\n\x12wal_segments_ahead\x18\x02 \x01(\x04H\x01\x88\x01\x01\x42\x12\n\x10_wal_capacity_mbB\x15\n\x13_wal_segments_ahead\"\xe6\x04\n\x14OptimizersConfigDiff\x12\x1e\n\x11\x64\x65leted_threshold\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12%\n\x18vacuum_min_vector_number\x18\x02 \x01(\x04H\x01\x88\x01\x01\x12#\n\x16\x64\x65\x66\x61ult_segment_number\x18\x03 \x01(\x04H\x02\x88\x01\x01\x12\x1d\n\x10max_segment_size\x18\x04 \x01(\x04H\x03\x88\x01\x01\x12\x1d\n\x10memmap_threshold\x18\x05 \x01(\x04H\x04\x88\x01\x01\x12\x1f\n\x12indexing_threshold\x18\x06 \x01(\x04H\x05\x88\x01\x01\x12\x1f\n\x12\x66lush_interval_sec\x18\x07 \x01(\x04H\x06\x88\x01\x01\x12\x30\n#deprecated_max_optimization_threads\x18\x08 \x01(\x04H\x07\x88\x01\x01\x12\x45\n\x18max_optimization_threads\x18\t \x01(\x0b\x32\x1e.qdrant.MaxOptimizationThreadsH\x08\x88\x01\x01\x42\x14\n\x12_deleted_thresholdB\x1b\n\x19_vacuum_min_vector_numberB\x19\n\x17_default_segment_numberB\x13\n\x11_max_segment_sizeB\x13\n\x11_memmap_thresholdB\x15\n\x13_indexing_thresholdB\x15\n\x13_flush_interval_secB&\n$_deprecated_max_optimization_threadsB\x1b\n\x19_max_optimization_threads\"\x88\x01\n\x12ScalarQuantization\x12&\n\x04type\x18\x01 \x01(\x0e\x32\x18.qdrant.QuantizationType\x12\x15\n\x08quantile\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x17\n\nalways_ram\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x0b\n\t_quantileB\r\n\x0b_always_ram\"l\n\x13ProductQuantization\x12-\n\x0b\x63ompression\x18\x01 \x01(\x0e\x32\x18.qdrant.CompressionRatio\x12\x17\n\nalways_ram\x18\x02 \x01(\x08H\x00\x88\x01\x01\x42\r\n\x0b_always_ram\"<\n\x12\x42inaryQuantization\x12\x17\n\nalways_ram\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\r\n\x0b_always_ram\"\xb0\x01\n\x12QuantizationConfig\x12,\n\x06scalar\x18\x01 \x01(\x0b\x32\x1a.qdrant.ScalarQuantizationH\x00\x12.\n\x07product\x18\x02 \x01(\x0b\x32\x1b.qdrant.ProductQuantizationH\x00\x12,\n\x06\x62inary\x18\x03 \x01(\x0b\x32\x1a.qdrant.BinaryQuantizationH\x00\x42\x0e\n\x0cquantization\"\n\n\x08\x44isabled\"\xda\x01\n\x16QuantizationConfigDiff\x12,\n\x06scalar\x18\x01 \x01(\x0b\x32\x1a.qdrant.ScalarQuantizationH\x00\x12.\n\x07product\x18\x02 \x01(\x0b\x32\x1b.qdrant.ProductQuantizationH\x00\x12$\n\x08\x64isabled\x18\x03 \x01(\x0b\x32\x10.qdrant.DisabledH\x00\x12,\n\x06\x62inary\x18\x04 \x01(\x0b\x32\x1a.qdrant.BinaryQuantizationH\x00\x42\x0e\n\x0cquantization\"\xf7\x08\n\x10StrictModeConfig\x12\x14\n\x07\x65nabled\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0fmax_query_limit\x18\x02 \x01(\rH\x01\x88\x01\x01\x12\x18\n\x0bmax_timeout\x18\x03 \x01(\rH\x02\x88\x01\x01\x12)\n\x1cunindexed_filtering_retrieve\x18\x04 \x01(\x08H\x03\x88\x01\x01\x12\'\n\x1aunindexed_filtering_update\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x1f\n\x12search_max_hnsw_ef\x18\x06 \x01(\rH\x05\x88\x01\x01\x12\x1f\n\x12search_allow_exact\x18\x07 \x01(\x08H\x06\x88\x01\x01\x12$\n\x17search_max_oversampling\x18\x08 \x01(\x02H\x07\x88\x01\x01\x12!\n\x14upsert_max_batchsize\x18\t \x01(\x04H\x08\x88\x01\x01\x12-\n max_collection_vector_size_bytes\x18\n \x01(\x04H\t\x88\x01\x01\x12\x1c\n\x0fread_rate_limit\x18\x0b \x01(\rH\n\x88\x01\x01\x12\x1d\n\x10write_rate_limit\x18\x0c \x01(\rH\x0b\x88\x01\x01\x12.\n!max_collection_payload_size_bytes\x18\r \x01(\x04H\x0c\x88\x01\x01\x12\"\n\x15\x66ilter_max_conditions\x18\x0e \x01(\x04H\r\x88\x01\x01\x12\x1f\n\x12\x63ondition_max_size\x18\x0f \x01(\x04H\x0e\x88\x01\x01\x12\x44\n\x12multivector_config\x18\x10 \x01(\x0b\x32#.qdrant.StrictModeMultivectorConfigH\x0f\x88\x01\x01\x12:\n\rsparse_config\x18\x11 \x01(\x0b\x32\x1e.qdrant.StrictModeSparseConfigH\x10\x88\x01\x01\x12\x1d\n\x10max_points_count\x18\x12 \x01(\x04H\x11\x88\x01\x01\x42\n\n\x08_enabledB\x12\n\x10_max_query_limitB\x0e\n\x0c_max_timeoutB\x1f\n\x1d_unindexed_filtering_retrieveB\x1d\n\x1b_unindexed_filtering_updateB\x15\n\x13_search_max_hnsw_efB\x15\n\x13_search_allow_exactB\x1a\n\x18_search_max_oversamplingB\x17\n\x15_upsert_max_batchsizeB#\n!_max_collection_vector_size_bytesB\x12\n\x10_read_rate_limitB\x13\n\x11_write_rate_limitB$\n\"_max_collection_payload_size_bytesB\x18\n\x16_filter_max_conditionsB\x15\n\x13_condition_max_sizeB\x15\n\x13_multivector_configB\x10\n\x0e_sparse_configB\x13\n\x11_max_points_count\"\xb0\x01\n\x16StrictModeSparseConfig\x12G\n\rsparse_config\x18\x01 \x03(\x0b\x32\x30.qdrant.StrictModeSparseConfig.SparseConfigEntry\x1aM\n\x11SparseConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.qdrant.StrictModeSparse:\x02\x38\x01\":\n\x10StrictModeSparse\x12\x17\n\nmax_length\x18\n \x01(\x04H\x00\x88\x01\x01\x42\r\n\x0b_max_length\"\xce\x01\n\x1bStrictModeMultivectorConfig\x12V\n\x12multivector_config\x18\x01 \x03(\x0b\x32:.qdrant.StrictModeMultivectorConfig.MultivectorConfigEntry\x1aW\n\x16MultivectorConfigEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12,\n\x05value\x18\x02 \x01(\x0b\x32\x1d.qdrant.StrictModeMultivector:\x02\x38\x01\"A\n\x15StrictModeMultivector\x12\x18\n\x0bmax_vectors\x18\x01 \x01(\x04H\x00\x88\x01\x01\x42\x0e\n\x0c_max_vectors\"\xd7\x07\n\x10\x43reateCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x30\n\x0bhnsw_config\x18\x04 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x00\x88\x01\x01\x12.\n\nwal_config\x18\x05 \x01(\x0b\x32\x15.qdrant.WalConfigDiffH\x01\x88\x01\x01\x12<\n\x11optimizers_config\x18\x06 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiffH\x02\x88\x01\x01\x12\x19\n\x0cshard_number\x18\x07 \x01(\rH\x03\x88\x01\x01\x12\x1c\n\x0fon_disk_payload\x18\x08 \x01(\x08H\x04\x88\x01\x01\x12\x14\n\x07timeout\x18\t \x01(\x04H\x05\x88\x01\x01\x12\x32\n\x0evectors_config\x18\n \x01(\x0b\x32\x15.qdrant.VectorsConfigH\x06\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x0b \x01(\rH\x07\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x0c \x01(\rH\x08\x88\x01\x01\x12!\n\x14init_from_collection\x18\r \x01(\tH\t\x88\x01\x01\x12<\n\x13quantization_config\x18\x0e \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\n\x88\x01\x01\x12\x34\n\x0fsharding_method\x18\x0f \x01(\x0e\x32\x16.qdrant.ShardingMethodH\x0b\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\x10 \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x0c\x88\x01\x01\x12\x39\n\x12strict_mode_config\x18\x11 \x01(\x0b\x32\x18.qdrant.StrictModeConfigH\r\x88\x01\x01\x42\x0e\n\x0c_hnsw_configB\r\n\x0b_wal_configB\x14\n\x12_optimizers_configB\x0f\n\r_shard_numberB\x12\n\x10_on_disk_payloadB\n\n\x08_timeoutB\x11\n\x0f_vectors_configB\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x17\n\x15_init_from_collectionB\x16\n\x14_quantization_configB\x12\n\x10_sharding_methodB\x18\n\x16_sparse_vectors_configB\x15\n\x13_strict_mode_configJ\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"\xf2\x04\n\x10UpdateCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12<\n\x11optimizers_config\x18\x02 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiffH\x00\x88\x01\x01\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x31\n\x06params\x18\x04 \x01(\x0b\x32\x1c.qdrant.CollectionParamsDiffH\x02\x88\x01\x01\x12\x30\n\x0bhnsw_config\x18\x05 \x01(\x0b\x32\x16.qdrant.HnswConfigDiffH\x03\x88\x01\x01\x12\x36\n\x0evectors_config\x18\x06 \x01(\x0b\x32\x19.qdrant.VectorsConfigDiffH\x04\x88\x01\x01\x12@\n\x13quantization_config\x18\x07 \x01(\x0b\x32\x1e.qdrant.QuantizationConfigDiffH\x05\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\x08 \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x06\x88\x01\x01\x12\x39\n\x12strict_mode_config\x18\t \x01(\x0b\x32\x18.qdrant.StrictModeConfigH\x07\x88\x01\x01\x42\x14\n\x12_optimizers_configB\n\n\x08_timeoutB\t\n\x07_paramsB\x0e\n\x0c_hnsw_configB\x11\n\x0f_vectors_configB\x16\n\x14_quantization_configB\x18\n\x16_sparse_vectors_configB\x15\n\x13_strict_mode_config\"M\n\x10\x44\x65leteCollection\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x14\n\x07timeout\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\";\n\x1b\x43ollectionOperationResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0c\n\x04time\x18\x02 \x01(\x01\"\xee\x03\n\x10\x43ollectionParams\x12\x14\n\x0cshard_number\x18\x03 \x01(\r\x12\x17\n\x0fon_disk_payload\x18\x04 \x01(\x08\x12\x32\n\x0evectors_config\x18\x05 \x01(\x0b\x32\x15.qdrant.VectorsConfigH\x00\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x06 \x01(\rH\x01\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x07 \x01(\rH\x02\x88\x01\x01\x12 \n\x13read_fan_out_factor\x18\x08 \x01(\rH\x03\x88\x01\x01\x12\x34\n\x0fsharding_method\x18\t \x01(\x0e\x32\x16.qdrant.ShardingMethodH\x04\x88\x01\x01\x12>\n\x15sparse_vectors_config\x18\n \x01(\x0b\x32\x1a.qdrant.SparseVectorConfigH\x05\x88\x01\x01\x42\x11\n\x0f_vectors_configB\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x16\n\x14_read_fan_out_factorB\x12\n\x10_sharding_methodB\x18\n\x16_sparse_vectors_configJ\x04\x08\x01\x10\x02J\x04\x08\x02\x10\x03\"\xfe\x01\n\x14\x43ollectionParamsDiff\x12\x1f\n\x12replication_factor\x18\x01 \x01(\rH\x00\x88\x01\x01\x12%\n\x18write_consistency_factor\x18\x02 \x01(\rH\x01\x88\x01\x01\x12\x1c\n\x0fon_disk_payload\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12 \n\x13read_fan_out_factor\x18\x04 \x01(\rH\x03\x88\x01\x01\x42\x15\n\x13_replication_factorB\x1b\n\x19_write_consistency_factorB\x12\n\x10_on_disk_payloadB\x16\n\x14_read_fan_out_factor\"\xf4\x02\n\x10\x43ollectionConfig\x12(\n\x06params\x18\x01 \x01(\x0b\x32\x18.qdrant.CollectionParams\x12+\n\x0bhnsw_config\x18\x02 \x01(\x0b\x32\x16.qdrant.HnswConfigDiff\x12\x36\n\x10optimizer_config\x18\x03 \x01(\x0b\x32\x1c.qdrant.OptimizersConfigDiff\x12)\n\nwal_config\x18\x04 \x01(\x0b\x32\x15.qdrant.WalConfigDiff\x12<\n\x13quantization_config\x18\x05 \x01(\x0b\x32\x1a.qdrant.QuantizationConfigH\x00\x88\x01\x01\x12\x39\n\x12strict_mode_config\x18\x06 \x01(\x0b\x32\x18.qdrant.StrictModeConfigH\x01\x88\x01\x01\x42\x16\n\x14_quantization_configB\x15\n\x13_strict_mode_config\"\\\n\x12KeywordIndexParams\x12\x16\n\tis_tenant\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x14\n\x07on_disk\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x0c\n\n_is_tenantB\n\n\x08_on_disk\"\xa0\x01\n\x12IntegerIndexParams\x12\x13\n\x06lookup\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x12\n\x05range\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x19\n\x0cis_principal\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12\x14\n\x07on_disk\x18\x04 \x01(\x08H\x03\x88\x01\x01\x42\t\n\x07_lookupB\x08\n\x06_rangeB\x0f\n\r_is_principalB\n\n\x08_on_disk\"`\n\x10\x46loatIndexParams\x12\x14\n\x07on_disk\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x19\n\x0cis_principal\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\n\n\x08_on_diskB\x0f\n\r_is_principal\"2\n\x0eGeoIndexParams\x12\x14\n\x07on_disk\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\n\n\x08_on_disk\"\xdf\x01\n\x0fTextIndexParams\x12(\n\ttokenizer\x18\x01 \x01(\x0e\x32\x15.qdrant.TokenizerType\x12\x16\n\tlowercase\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1a\n\rmin_token_len\x18\x03 \x01(\x04H\x01\x88\x01\x01\x12\x1a\n\rmax_token_len\x18\x04 \x01(\x04H\x02\x88\x01\x01\x12\x14\n\x07on_disk\x18\x05 \x01(\x08H\x03\x88\x01\x01\x42\x0c\n\n_lowercaseB\x10\n\x0e_min_token_lenB\x10\n\x0e_max_token_lenB\n\n\x08_on_disk\"3\n\x0f\x42oolIndexParams\x12\x14\n\x07on_disk\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42\n\n\x08_on_disk\"c\n\x13\x44\x61tetimeIndexParams\x12\x14\n\x07on_disk\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x19\n\x0cis_principal\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\n\n\x08_on_diskB\x0f\n\r_is_principal\"Y\n\x0fUuidIndexParams\x12\x16\n\tis_tenant\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x14\n\x07on_disk\x18\x02 \x01(\x08H\x01\x88\x01\x01\x42\x0c\n\n_is_tenantB\n\n\x08_on_disk\"\xe8\x03\n\x12PayloadIndexParams\x12:\n\x14keyword_index_params\x18\x03 \x01(\x0b\x32\x1a.qdrant.KeywordIndexParamsH\x00\x12:\n\x14integer_index_params\x18\x02 \x01(\x0b\x32\x1a.qdrant.IntegerIndexParamsH\x00\x12\x36\n\x12\x66loat_index_params\x18\x04 \x01(\x0b\x32\x18.qdrant.FloatIndexParamsH\x00\x12\x32\n\x10geo_index_params\x18\x05 \x01(\x0b\x32\x16.qdrant.GeoIndexParamsH\x00\x12\x34\n\x11text_index_params\x18\x01 \x01(\x0b\x32\x17.qdrant.TextIndexParamsH\x00\x12\x34\n\x11\x62ool_index_params\x18\x06 \x01(\x0b\x32\x17.qdrant.BoolIndexParamsH\x00\x12<\n\x15\x64\x61tetime_index_params\x18\x07 \x01(\x0b\x32\x1b.qdrant.DatetimeIndexParamsH\x00\x12\x34\n\x11uuid_index_params\x18\x08 \x01(\x0b\x32\x17.qdrant.UuidIndexParamsH\x00\x42\x0e\n\x0cindex_params\"\x9d\x01\n\x11PayloadSchemaInfo\x12,\n\tdata_type\x18\x01 \x01(\x0e\x32\x19.qdrant.PayloadSchemaType\x12/\n\x06params\x18\x02 \x01(\x0b\x32\x1a.qdrant.PayloadIndexParamsH\x00\x88\x01\x01\x12\x13\n\x06points\x18\x03 \x01(\x04H\x01\x88\x01\x01\x42\t\n\x07_paramsB\t\n\x07_points\"\xe7\x03\n\x0e\x43ollectionInfo\x12(\n\x06status\x18\x01 \x01(\x0e\x32\x18.qdrant.CollectionStatus\x12\x31\n\x10optimizer_status\x18\x02 \x01(\x0b\x32\x17.qdrant.OptimizerStatus\x12\x1a\n\rvectors_count\x18\x03 \x01(\x04H\x00\x88\x01\x01\x12\x16\n\x0esegments_count\x18\x04 \x01(\x04\x12(\n\x06\x63onfig\x18\x07 \x01(\x0b\x32\x18.qdrant.CollectionConfig\x12\x41\n\x0epayload_schema\x18\x08 \x03(\x0b\x32).qdrant.CollectionInfo.PayloadSchemaEntry\x12\x19\n\x0cpoints_count\x18\t \x01(\x04H\x01\x88\x01\x01\x12\"\n\x15indexed_vectors_count\x18\n \x01(\x04H\x02\x88\x01\x01\x1aO\n\x12PayloadSchemaEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.qdrant.PayloadSchemaInfo:\x02\x38\x01\x42\x10\n\x0e_vectors_countB\x0f\n\r_points_countB\x18\n\x16_indexed_vectors_countJ\x04\x08\x05\x10\x06J\x04\x08\x06\x10\x07\"[\n\rChangeAliases\x12(\n\x07\x61\x63tions\x18\x01 \x03(\x0b\x32\x17.qdrant.AliasOperations\x12\x14\n\x07timeout\x18\x02 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"\xa2\x01\n\x0f\x41liasOperations\x12+\n\x0c\x63reate_alias\x18\x01 \x01(\x0b\x32\x13.qdrant.CreateAliasH\x00\x12+\n\x0crename_alias\x18\x02 \x01(\x0b\x32\x13.qdrant.RenameAliasH\x00\x12+\n\x0c\x64\x65lete_alias\x18\x03 \x01(\x0b\x32\x13.qdrant.DeleteAliasH\x00\x42\x08\n\x06\x61\x63tion\":\n\x0b\x43reateAlias\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\x12\n\nalias_name\x18\x02 \x01(\t\"=\n\x0bRenameAlias\x12\x16\n\x0eold_alias_name\x18\x01 \x01(\t\x12\x16\n\x0enew_alias_name\x18\x02 \x01(\t\"!\n\x0b\x44\x65leteAlias\x12\x12\n\nalias_name\x18\x01 \x01(\t\"\x14\n\x12ListAliasesRequest\"7\n\x1cListCollectionAliasesRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"?\n\x10\x41liasDescription\x12\x12\n\nalias_name\x18\x01 \x01(\t\x12\x17\n\x0f\x63ollection_name\x18\x02 \x01(\t\"N\n\x13ListAliasesResponse\x12)\n\x07\x61liases\x18\x01 \x03(\x0b\x32\x18.qdrant.AliasDescription\x12\x0c\n\x04time\x18\x02 \x01(\x01\"7\n\x1c\x43ollectionClusterInfoRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\"6\n\x08ShardKey\x12\x11\n\x07keyword\x18\x01 \x01(\tH\x00\x12\x10\n\x06number\x18\x02 \x01(\x04H\x00\x42\x05\n\x03key\"\x95\x01\n\x0eLocalShardInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x14\n\x0cpoints_count\x18\x02 \x01(\x04\x12#\n\x05state\x18\x03 \x01(\x0e\x32\x14.qdrant.ReplicaState\x12(\n\tshard_key\x18\x04 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x00\x88\x01\x01\x42\x0c\n\n_shard_key\"\x91\x01\n\x0fRemoteShardInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0f\n\x07peer_id\x18\x02 \x01(\x04\x12#\n\x05state\x18\x03 \x01(\x0e\x32\x14.qdrant.ReplicaState\x12(\n\tshard_key\x18\x04 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x00\x88\x01\x01\x42\x0c\n\n_shard_key\"w\n\x11ShardTransferInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x18\n\x0bto_shard_id\x18\x05 \x01(\rH\x00\x88\x01\x01\x12\x0c\n\x04\x66rom\x18\x02 \x01(\x04\x12\n\n\x02to\x18\x03 \x01(\x04\x12\x0c\n\x04sync\x18\x04 \x01(\x08\x42\x0e\n\x0c_to_shard_id\"\x9b\x01\n\x0eReshardingInfo\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0f\n\x07peer_id\x18\x02 \x01(\x04\x12(\n\tshard_key\x18\x03 \x01(\x0b\x32\x10.qdrant.ShardKeyH\x00\x88\x01\x01\x12.\n\tdirection\x18\x04 \x01(\x0e\x32\x1b.qdrant.ReshardingDirectionB\x0c\n\n_shard_key\"\x8e\x02\n\x1d\x43ollectionClusterInfoResponse\x12\x0f\n\x07peer_id\x18\x01 \x01(\x04\x12\x13\n\x0bshard_count\x18\x02 \x01(\x04\x12,\n\x0clocal_shards\x18\x03 \x03(\x0b\x32\x16.qdrant.LocalShardInfo\x12.\n\rremote_shards\x18\x04 \x03(\x0b\x32\x17.qdrant.RemoteShardInfo\x12\x32\n\x0fshard_transfers\x18\x05 \x03(\x0b\x32\x19.qdrant.ShardTransferInfo\x12\x35\n\x15resharding_operations\x18\x06 \x03(\x0b\x32\x16.qdrant.ReshardingInfo\"\xae\x01\n\tMoveShard\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x18\n\x0bto_shard_id\x18\x05 \x01(\rH\x00\x88\x01\x01\x12\x14\n\x0c\x66rom_peer_id\x18\x02 \x01(\x04\x12\x12\n\nto_peer_id\x18\x03 \x01(\x04\x12\x30\n\x06method\x18\x04 \x01(\x0e\x32\x1b.qdrant.ShardTransferMethodH\x01\x88\x01\x01\x42\x0e\n\x0c_to_shard_idB\t\n\x07_method\"\xb3\x01\n\x0eReplicateShard\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x18\n\x0bto_shard_id\x18\x05 \x01(\rH\x00\x88\x01\x01\x12\x14\n\x0c\x66rom_peer_id\x18\x02 \x01(\x04\x12\x12\n\nto_peer_id\x18\x03 \x01(\x04\x12\x30\n\x06method\x18\x04 \x01(\x0e\x32\x1b.qdrant.ShardTransferMethodH\x01\x88\x01\x01\x42\x0e\n\x0c_to_shard_idB\t\n\x07_method\"z\n\x12\x41\x62ortShardTransfer\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x18\n\x0bto_shard_id\x18\x04 \x01(\rH\x00\x88\x01\x01\x12\x14\n\x0c\x66rom_peer_id\x18\x02 \x01(\x04\x12\x12\n\nto_peer_id\x18\x03 \x01(\x04\x42\x0e\n\x0c_to_shard_id\"\xa4\x01\n\x0fRestartTransfer\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x18\n\x0bto_shard_id\x18\x05 \x01(\rH\x00\x88\x01\x01\x12\x14\n\x0c\x66rom_peer_id\x18\x02 \x01(\x04\x12\x12\n\nto_peer_id\x18\x03 \x01(\x04\x12+\n\x06method\x18\x04 \x01(\x0e\x32\x1b.qdrant.ShardTransferMethodB\x0e\n\x0c_to_shard_id\",\n\x07Replica\x12\x10\n\x08shard_id\x18\x01 \x01(\r\x12\x0f\n\x07peer_id\x18\x02 \x01(\x04\"\xae\x01\n\x0e\x43reateShardKey\x12#\n\tshard_key\x18\x01 \x01(\x0b\x32\x10.qdrant.ShardKey\x12\x1a\n\rshards_number\x18\x02 \x01(\rH\x00\x88\x01\x01\x12\x1f\n\x12replication_factor\x18\x03 \x01(\rH\x01\x88\x01\x01\x12\x11\n\tplacement\x18\x04 \x03(\x04\x42\x10\n\x0e_shards_numberB\x15\n\x13_replication_factor\"5\n\x0e\x44\x65leteShardKey\x12#\n\tshard_key\x18\x01 \x01(\x0b\x32\x10.qdrant.ShardKey\"\xc5\x03\n#UpdateCollectionClusterSetupRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\nmove_shard\x18\x02 \x01(\x0b\x32\x11.qdrant.MoveShardH\x00\x12\x31\n\x0freplicate_shard\x18\x03 \x01(\x0b\x32\x16.qdrant.ReplicateShardH\x00\x12\x34\n\x0e\x61\x62ort_transfer\x18\x04 \x01(\x0b\x32\x1a.qdrant.AbortShardTransferH\x00\x12\'\n\x0c\x64rop_replica\x18\x05 \x01(\x0b\x32\x0f.qdrant.ReplicaH\x00\x12\x32\n\x10\x63reate_shard_key\x18\x07 \x01(\x0b\x32\x16.qdrant.CreateShardKeyH\x00\x12\x32\n\x10\x64\x65lete_shard_key\x18\x08 \x01(\x0b\x32\x16.qdrant.DeleteShardKeyH\x00\x12\x33\n\x10restart_transfer\x18\t \x01(\x0b\x32\x17.qdrant.RestartTransferH\x00\x12\x14\n\x07timeout\x18\x06 \x01(\x04H\x01\x88\x01\x01\x42\x0b\n\toperationB\n\n\x08_timeout\"6\n$UpdateCollectionClusterSetupResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"{\n\x15\x43reateShardKeyRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x07request\x18\x02 \x01(\x0b\x32\x16.qdrant.CreateShardKey\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"{\n\x15\x44\x65leteShardKeyRequest\x12\x17\n\x0f\x63ollection_name\x18\x01 \x01(\t\x12\'\n\x07request\x18\x02 \x01(\x0b\x32\x16.qdrant.DeleteShardKey\x12\x14\n\x07timeout\x18\x03 \x01(\x04H\x00\x88\x01\x01\x42\n\n\x08_timeout\"(\n\x16\x43reateShardKeyResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x16\x44\x65leteShardKeyResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08*<\n\x08\x44\x61tatype\x12\x0b\n\x07\x44\x65\x66\x61ult\x10\x00\x12\x0b\n\x07\x46loat32\x10\x01\x12\t\n\x05Uint8\x10\x02\x12\x0b\n\x07\x46loat16\x10\x03*\x1d\n\x08Modifier\x12\x08\n\x04None\x10\x00\x12\x07\n\x03Idf\x10\x01*#\n\x15MultiVectorComparator\x12\n\n\x06MaxSim\x10\x00*O\n\x08\x44istance\x12\x13\n\x0fUnknownDistance\x10\x00\x12\n\n\x06\x43osine\x10\x01\x12\n\n\x06\x45uclid\x10\x02\x12\x07\n\x03\x44ot\x10\x03\x12\r\n\tManhattan\x10\x04*Y\n\x10\x43ollectionStatus\x12\x1b\n\x17UnknownCollectionStatus\x10\x00\x12\t\n\x05Green\x10\x01\x12\n\n\x06Yellow\x10\x02\x12\x07\n\x03Red\x10\x03\x12\x08\n\x04Grey\x10\x04*~\n\x11PayloadSchemaType\x12\x0f\n\x0bUnknownType\x10\x00\x12\x0b\n\x07Keyword\x10\x01\x12\x0b\n\x07Integer\x10\x02\x12\t\n\x05\x46loat\x10\x03\x12\x07\n\x03Geo\x10\x04\x12\x08\n\x04Text\x10\x05\x12\x08\n\x04\x42ool\x10\x06\x12\x0c\n\x08\x44\x61tetime\x10\x07\x12\x08\n\x04Uuid\x10\x08*5\n\x10QuantizationType\x12\x17\n\x13UnknownQuantization\x10\x00\x12\x08\n\x04Int8\x10\x01*=\n\x10\x43ompressionRatio\x12\x06\n\x02x4\x10\x00\x12\x06\n\x02x8\x10\x01\x12\x07\n\x03x16\x10\x02\x12\x07\n\x03x32\x10\x03\x12\x07\n\x03x64\x10\x04*&\n\x0eShardingMethod\x12\x08\n\x04\x41uto\x10\x00\x12\n\n\x06\x43ustom\x10\x01*T\n\rTokenizerType\x12\x0b\n\x07Unknown\x10\x00\x12\n\n\x06Prefix\x10\x01\x12\x0e\n\nWhitespace\x10\x02\x12\x08\n\x04Word\x10\x03\x12\x10\n\x0cMultilingual\x10\x04*\x9d\x01\n\x0cReplicaState\x12\n\n\x06\x41\x63tive\x10\x00\x12\x08\n\x04\x44\x65\x61\x64\x10\x01\x12\x0b\n\x07Partial\x10\x02\x12\x10\n\x0cInitializing\x10\x03\x12\x0c\n\x08Listener\x10\x04\x12\x13\n\x0fPartialSnapshot\x10\x05\x12\x0c\n\x08Recovery\x10\x06\x12\x0e\n\nResharding\x10\x07\x12\x17\n\x13ReshardingScaleDown\x10\x08*\'\n\x13ReshardingDirection\x12\x06\n\x02Up\x10\x00\x12\x08\n\x04\x44own\x10\x01*a\n\x13ShardTransferMethod\x12\x11\n\rStreamRecords\x10\x00\x12\x0c\n\x08Snapshot\x10\x01\x12\x0c\n\x08WalDelta\x10\x02\x12\x1b\n\x17ReshardingStreamRecords\x10\x03\x42\x15\xaa\x02\x12Qdrant.Client.Grpcb\x06proto3')

_DATATYPE = DESCRIPTOR.enum_types_by_name['Datatype']
Datatype = enum_type_wrapper.EnumTypeWrapper(_DATATYPE)
_MODIFIER = DESCRIPTOR.enum_types_by_name['Modifier']
Modifier = enum_type_wrapper.EnumTypeWrapper(_MODIFIER)
_MULTIVECTORCOMPARATOR = DESCRIPTOR.enum_types_by_name['MultiVectorComparator']
MultiVectorComparator = enum_type_wrapper.EnumTypeWrapper(_MULTIVECTORCOMPARATOR)
_DISTANCE = DESCRIPTOR.enum_types_by_name['Distance']
Distance = enum_type_wrapper.EnumTypeWrapper(_DISTANCE)
_COLLECTIONSTATUS = DESCRIPTOR.enum_types_by_name['CollectionStatus']
CollectionStatus = enum_type_wrapper.EnumTypeWrapper(_COLLECTIONSTATUS)
_PAYLOADSCHEMATYPE = DESCRIPTOR.enum_types_by_name['PayloadSchemaType']
PayloadSchemaType = enum_type_wrapper.EnumTypeWrapper(_PAYLOADSCHEMATYPE)
_QUANTIZATIONTYPE = DESCRIPTOR.enum_types_by_name['QuantizationType']
QuantizationType = enum_type_wrapper.EnumTypeWrapper(_QUANTIZATIONTYPE)
_COMPRESSIONRATIO = DESCRIPTOR.enum_types_by_name['CompressionRatio']
CompressionRatio = enum_type_wrapper.EnumTypeWrapper(_COMPRESSIONRATIO)
_SHARDINGMETHOD = DESCRIPTOR.enum_types_by_name['ShardingMethod']
ShardingMethod = enum_type_wrapper.EnumTypeWrapper(_SHARDINGMETHOD)
_TOKENIZERTYPE = DESCRIPTOR.enum_types_by_name['TokenizerType']
TokenizerType = enum_type_wrapper.EnumTypeWrapper(_TOKENIZERTYPE)
_REPLICASTATE = DESCRIPTOR.enum_types_by_name['ReplicaState']
ReplicaState = enum_type_wrapper.EnumTypeWrapper(_REPLICASTATE)
_RESHARDINGDIRECTION = DESCRIPTOR.enum_types_by_name['ReshardingDirection']
ReshardingDirection = enum_type_wrapper.EnumTypeWrapper(_RESHARDINGDIRECTION)
_SHARDTRANSFERMETHOD = DESCRIPTOR.enum_types_by_name['ShardTransferMethod']
ShardTransferMethod = enum_type_wrapper.EnumTypeWrapper(_SHARDTRANSFERMETHOD)
Default = 0
Float32 = 1
Uint8 = 2
Float16 = 3
globals()['None'] = 0
Idf = 1
MaxSim = 0
UnknownDistance = 0
Cosine = 1
Euclid = 2
Dot = 3
Manhattan = 4
UnknownCollectionStatus = 0
Green = 1
Yellow = 2
Red = 3
Grey = 4
UnknownType = 0
Keyword = 1
Integer = 2
Float = 3
Geo = 4
Text = 5
Bool = 6
Datetime = 7
Uuid = 8
UnknownQuantization = 0
Int8 = 1
x4 = 0
x8 = 1
x16 = 2
x32 = 3
x64 = 4
Auto = 0
Custom = 1
Unknown = 0
Prefix = 1
Whitespace = 2
Word = 3
Multilingual = 4
Active = 0
Dead = 1
Partial = 2
Initializing = 3
Listener = 4
PartialSnapshot = 5
Recovery = 6
Resharding = 7
ReshardingScaleDown = 8
Up = 0
Down = 1
StreamRecords = 0
Snapshot = 1
WalDelta = 2
ReshardingStreamRecords = 3


_VECTORPARAMS = DESCRIPTOR.message_types_by_name['VectorParams']
_VECTORPARAMSDIFF = DESCRIPTOR.message_types_by_name['VectorParamsDiff']
_VECTORPARAMSMAP = DESCRIPTOR.message_types_by_name['VectorParamsMap']
_VECTORPARAMSMAP_MAPENTRY = _VECTORPARAMSMAP.nested_types_by_name['MapEntry']
_VECTORPARAMSDIFFMAP = DESCRIPTOR.message_types_by_name['VectorParamsDiffMap']
_VECTORPARAMSDIFFMAP_MAPENTRY = _VECTORPARAMSDIFFMAP.nested_types_by_name['MapEntry']
_VECTORSCONFIG = DESCRIPTOR.message_types_by_name['VectorsConfig']
_VECTORSCONFIGDIFF = DESCRIPTOR.message_types_by_name['VectorsConfigDiff']
_SPARSEVECTORPARAMS = DESCRIPTOR.message_types_by_name['SparseVectorParams']
_SPARSEVECTORCONFIG = DESCRIPTOR.message_types_by_name['SparseVectorConfig']
_SPARSEVECTORCONFIG_MAPENTRY = _SPARSEVECTORCONFIG.nested_types_by_name['MapEntry']
_MULTIVECTORCONFIG = DESCRIPTOR.message_types_by_name['MultiVectorConfig']
_GETCOLLECTIONINFOREQUEST = DESCRIPTOR.message_types_by_name['GetCollectionInfoRequest']
_COLLECTIONEXISTSREQUEST = DESCRIPTOR.message_types_by_name['CollectionExistsRequest']
_COLLECTIONEXISTS = DESCRIPTOR.message_types_by_name['CollectionExists']
_COLLECTIONEXISTSRESPONSE = DESCRIPTOR.message_types_by_name['CollectionExistsResponse']
_LISTCOLLECTIONSREQUEST = DESCRIPTOR.message_types_by_name['ListCollectionsRequest']
_COLLECTIONDESCRIPTION = DESCRIPTOR.message_types_by_name['CollectionDescription']
_GETCOLLECTIONINFORESPONSE = DESCRIPTOR.message_types_by_name['GetCollectionInfoResponse']
_LISTCOLLECTIONSRESPONSE = DESCRIPTOR.message_types_by_name['ListCollectionsResponse']
_MAXOPTIMIZATIONTHREADS = DESCRIPTOR.message_types_by_name['MaxOptimizationThreads']
_OPTIMIZERSTATUS = DESCRIPTOR.message_types_by_name['OptimizerStatus']
_HNSWCONFIGDIFF = DESCRIPTOR.message_types_by_name['HnswConfigDiff']
_SPARSEINDEXCONFIG = DESCRIPTOR.message_types_by_name['SparseIndexConfig']
_WALCONFIGDIFF = DESCRIPTOR.message_types_by_name['WalConfigDiff']
_OPTIMIZERSCONFIGDIFF = DESCRIPTOR.message_types_by_name['OptimizersConfigDiff']
_SCALARQUANTIZATION = DESCRIPTOR.message_types_by_name['ScalarQuantization']
_PRODUCTQUANTIZATION = DESCRIPTOR.message_types_by_name['ProductQuantization']
_BINARYQUANTIZATION = DESCRIPTOR.message_types_by_name['BinaryQuantization']
_QUANTIZATIONCONFIG = DESCRIPTOR.message_types_by_name['QuantizationConfig']
_DISABLED = DESCRIPTOR.message_types_by_name['Disabled']
_QUANTIZATIONCONFIGDIFF = DESCRIPTOR.message_types_by_name['QuantizationConfigDiff']
_STRICTMODECONFIG = DESCRIPTOR.message_types_by_name['StrictModeConfig']
_STRICTMODESPARSECONFIG = DESCRIPTOR.message_types_by_name['StrictModeSparseConfig']
_STRICTMODESPARSECONFIG_SPARSECONFIGENTRY = _STRICTMODESPARSECONFIG.nested_types_by_name['SparseConfigEntry']
_STRICTMODESPARSE = DESCRIPTOR.message_types_by_name['StrictModeSparse']
_STRICTMODEMULTIVECTORCONFIG = DESCRIPTOR.message_types_by_name['StrictModeMultivectorConfig']
_STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY = _STRICTMODEMULTIVECTORCONFIG.nested_types_by_name['MultivectorConfigEntry']
_STRICTMODEMULTIVECTOR = DESCRIPTOR.message_types_by_name['StrictModeMultivector']
_CREATECOLLECTION = DESCRIPTOR.message_types_by_name['CreateCollection']
_UPDATECOLLECTION = DESCRIPTOR.message_types_by_name['UpdateCollection']
_DELETECOLLECTION = DESCRIPTOR.message_types_by_name['DeleteCollection']
_COLLECTIONOPERATIONRESPONSE = DESCRIPTOR.message_types_by_name['CollectionOperationResponse']
_COLLECTIONPARAMS = DESCRIPTOR.message_types_by_name['CollectionParams']
_COLLECTIONPARAMSDIFF = DESCRIPTOR.message_types_by_name['CollectionParamsDiff']
_COLLECTIONCONFIG = DESCRIPTOR.message_types_by_name['CollectionConfig']
_KEYWORDINDEXPARAMS = DESCRIPTOR.message_types_by_name['KeywordIndexParams']
_INTEGERINDEXPARAMS = DESCRIPTOR.message_types_by_name['IntegerIndexParams']
_FLOATINDEXPARAMS = DESCRIPTOR.message_types_by_name['FloatIndexParams']
_GEOINDEXPARAMS = DESCRIPTOR.message_types_by_name['GeoIndexParams']
_TEXTINDEXPARAMS = DESCRIPTOR.message_types_by_name['TextIndexParams']
_BOOLINDEXPARAMS = DESCRIPTOR.message_types_by_name['BoolIndexParams']
_DATETIMEINDEXPARAMS = DESCRIPTOR.message_types_by_name['DatetimeIndexParams']
_UUIDINDEXPARAMS = DESCRIPTOR.message_types_by_name['UuidIndexParams']
_PAYLOADINDEXPARAMS = DESCRIPTOR.message_types_by_name['PayloadIndexParams']
_PAYLOADSCHEMAINFO = DESCRIPTOR.message_types_by_name['PayloadSchemaInfo']
_COLLECTIONINFO = DESCRIPTOR.message_types_by_name['CollectionInfo']
_COLLECTIONINFO_PAYLOADSCHEMAENTRY = _COLLECTIONINFO.nested_types_by_name['PayloadSchemaEntry']
_CHANGEALIASES = DESCRIPTOR.message_types_by_name['ChangeAliases']
_ALIASOPERATIONS = DESCRIPTOR.message_types_by_name['AliasOperations']
_CREATEALIAS = DESCRIPTOR.message_types_by_name['CreateAlias']
_RENAMEALIAS = DESCRIPTOR.message_types_by_name['RenameAlias']
_DELETEALIAS = DESCRIPTOR.message_types_by_name['DeleteAlias']
_LISTALIASESREQUEST = DESCRIPTOR.message_types_by_name['ListAliasesRequest']
_LISTCOLLECTIONALIASESREQUEST = DESCRIPTOR.message_types_by_name['ListCollectionAliasesRequest']
_ALIASDESCRIPTION = DESCRIPTOR.message_types_by_name['AliasDescription']
_LISTALIASESRESPONSE = DESCRIPTOR.message_types_by_name['ListAliasesResponse']
_COLLECTIONCLUSTERINFOREQUEST = DESCRIPTOR.message_types_by_name['CollectionClusterInfoRequest']
_SHARDKEY = DESCRIPTOR.message_types_by_name['ShardKey']
_LOCALSHARDINFO = DESCRIPTOR.message_types_by_name['LocalShardInfo']
_REMOTESHARDINFO = DESCRIPTOR.message_types_by_name['RemoteShardInfo']
_SHARDTRANSFERINFO = DESCRIPTOR.message_types_by_name['ShardTransferInfo']
_RESHARDINGINFO = DESCRIPTOR.message_types_by_name['ReshardingInfo']
_COLLECTIONCLUSTERINFORESPONSE = DESCRIPTOR.message_types_by_name['CollectionClusterInfoResponse']
_MOVESHARD = DESCRIPTOR.message_types_by_name['MoveShard']
_REPLICATESHARD = DESCRIPTOR.message_types_by_name['ReplicateShard']
_ABORTSHARDTRANSFER = DESCRIPTOR.message_types_by_name['AbortShardTransfer']
_RESTARTTRANSFER = DESCRIPTOR.message_types_by_name['RestartTransfer']
_REPLICA = DESCRIPTOR.message_types_by_name['Replica']
_CREATESHARDKEY = DESCRIPTOR.message_types_by_name['CreateShardKey']
_DELETESHARDKEY = DESCRIPTOR.message_types_by_name['DeleteShardKey']
_UPDATECOLLECTIONCLUSTERSETUPREQUEST = DESCRIPTOR.message_types_by_name['UpdateCollectionClusterSetupRequest']
_UPDATECOLLECTIONCLUSTERSETUPRESPONSE = DESCRIPTOR.message_types_by_name['UpdateCollectionClusterSetupResponse']
_CREATESHARDKEYREQUEST = DESCRIPTOR.message_types_by_name['CreateShardKeyRequest']
_DELETESHARDKEYREQUEST = DESCRIPTOR.message_types_by_name['DeleteShardKeyRequest']
_CREATESHARDKEYRESPONSE = DESCRIPTOR.message_types_by_name['CreateShardKeyResponse']
_DELETESHARDKEYRESPONSE = DESCRIPTOR.message_types_by_name['DeleteShardKeyResponse']
_MAXOPTIMIZATIONTHREADS_SETTING = _MAXOPTIMIZATIONTHREADS.enum_types_by_name['Setting']
VectorParams = _reflection.GeneratedProtocolMessageType('VectorParams', (_message.Message,), {
  'DESCRIPTOR' : _VECTORPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParams)
  })
_sym_db.RegisterMessage(VectorParams)

VectorParamsDiff = _reflection.GeneratedProtocolMessageType('VectorParamsDiff', (_message.Message,), {
  'DESCRIPTOR' : _VECTORPARAMSDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiff)
  })
_sym_db.RegisterMessage(VectorParamsDiff)

VectorParamsMap = _reflection.GeneratedProtocolMessageType('VectorParamsMap', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _VECTORPARAMSMAP_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.VectorParamsMap.MapEntry)
    })
  ,
  'DESCRIPTOR' : _VECTORPARAMSMAP,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsMap)
  })
_sym_db.RegisterMessage(VectorParamsMap)
_sym_db.RegisterMessage(VectorParamsMap.MapEntry)

VectorParamsDiffMap = _reflection.GeneratedProtocolMessageType('VectorParamsDiffMap', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _VECTORPARAMSDIFFMAP_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiffMap.MapEntry)
    })
  ,
  'DESCRIPTOR' : _VECTORPARAMSDIFFMAP,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorParamsDiffMap)
  })
_sym_db.RegisterMessage(VectorParamsDiffMap)
_sym_db.RegisterMessage(VectorParamsDiffMap.MapEntry)

VectorsConfig = _reflection.GeneratedProtocolMessageType('VectorsConfig', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsConfig)
  })
_sym_db.RegisterMessage(VectorsConfig)

VectorsConfigDiff = _reflection.GeneratedProtocolMessageType('VectorsConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _VECTORSCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.VectorsConfigDiff)
  })
_sym_db.RegisterMessage(VectorsConfigDiff)

SparseVectorParams = _reflection.GeneratedProtocolMessageType('SparseVectorParams', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEVECTORPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseVectorParams)
  })
_sym_db.RegisterMessage(SparseVectorParams)

SparseVectorConfig = _reflection.GeneratedProtocolMessageType('SparseVectorConfig', (_message.Message,), {

  'MapEntry' : _reflection.GeneratedProtocolMessageType('MapEntry', (_message.Message,), {
    'DESCRIPTOR' : _SPARSEVECTORCONFIG_MAPENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.SparseVectorConfig.MapEntry)
    })
  ,
  'DESCRIPTOR' : _SPARSEVECTORCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseVectorConfig)
  })
_sym_db.RegisterMessage(SparseVectorConfig)
_sym_db.RegisterMessage(SparseVectorConfig.MapEntry)

MultiVectorConfig = _reflection.GeneratedProtocolMessageType('MultiVectorConfig', (_message.Message,), {
  'DESCRIPTOR' : _MULTIVECTORCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MultiVectorConfig)
  })
_sym_db.RegisterMessage(MultiVectorConfig)

GetCollectionInfoRequest = _reflection.GeneratedProtocolMessageType('GetCollectionInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCOLLECTIONINFOREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetCollectionInfoRequest)
  })
_sym_db.RegisterMessage(GetCollectionInfoRequest)

CollectionExistsRequest = _reflection.GeneratedProtocolMessageType('CollectionExistsRequest', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONEXISTSREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionExistsRequest)
  })
_sym_db.RegisterMessage(CollectionExistsRequest)

CollectionExists = _reflection.GeneratedProtocolMessageType('CollectionExists', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONEXISTS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionExists)
  })
_sym_db.RegisterMessage(CollectionExists)

CollectionExistsResponse = _reflection.GeneratedProtocolMessageType('CollectionExistsResponse', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONEXISTSRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionExistsResponse)
  })
_sym_db.RegisterMessage(CollectionExistsResponse)

ListCollectionsRequest = _reflection.GeneratedProtocolMessageType('ListCollectionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONSREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionsRequest)
  })
_sym_db.RegisterMessage(ListCollectionsRequest)

CollectionDescription = _reflection.GeneratedProtocolMessageType('CollectionDescription', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONDESCRIPTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionDescription)
  })
_sym_db.RegisterMessage(CollectionDescription)

GetCollectionInfoResponse = _reflection.GeneratedProtocolMessageType('GetCollectionInfoResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCOLLECTIONINFORESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GetCollectionInfoResponse)
  })
_sym_db.RegisterMessage(GetCollectionInfoResponse)

ListCollectionsResponse = _reflection.GeneratedProtocolMessageType('ListCollectionsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONSRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionsResponse)
  })
_sym_db.RegisterMessage(ListCollectionsResponse)

MaxOptimizationThreads = _reflection.GeneratedProtocolMessageType('MaxOptimizationThreads', (_message.Message,), {
  'DESCRIPTOR' : _MAXOPTIMIZATIONTHREADS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MaxOptimizationThreads)
  })
_sym_db.RegisterMessage(MaxOptimizationThreads)

OptimizerStatus = _reflection.GeneratedProtocolMessageType('OptimizerStatus', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZERSTATUS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OptimizerStatus)
  })
_sym_db.RegisterMessage(OptimizerStatus)

HnswConfigDiff = _reflection.GeneratedProtocolMessageType('HnswConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _HNSWCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.HnswConfigDiff)
  })
_sym_db.RegisterMessage(HnswConfigDiff)

SparseIndexConfig = _reflection.GeneratedProtocolMessageType('SparseIndexConfig', (_message.Message,), {
  'DESCRIPTOR' : _SPARSEINDEXCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.SparseIndexConfig)
  })
_sym_db.RegisterMessage(SparseIndexConfig)

WalConfigDiff = _reflection.GeneratedProtocolMessageType('WalConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _WALCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.WalConfigDiff)
  })
_sym_db.RegisterMessage(WalConfigDiff)

OptimizersConfigDiff = _reflection.GeneratedProtocolMessageType('OptimizersConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _OPTIMIZERSCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.OptimizersConfigDiff)
  })
_sym_db.RegisterMessage(OptimizersConfigDiff)

ScalarQuantization = _reflection.GeneratedProtocolMessageType('ScalarQuantization', (_message.Message,), {
  'DESCRIPTOR' : _SCALARQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ScalarQuantization)
  })
_sym_db.RegisterMessage(ScalarQuantization)

ProductQuantization = _reflection.GeneratedProtocolMessageType('ProductQuantization', (_message.Message,), {
  'DESCRIPTOR' : _PRODUCTQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ProductQuantization)
  })
_sym_db.RegisterMessage(ProductQuantization)

BinaryQuantization = _reflection.GeneratedProtocolMessageType('BinaryQuantization', (_message.Message,), {
  'DESCRIPTOR' : _BINARYQUANTIZATION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.BinaryQuantization)
  })
_sym_db.RegisterMessage(BinaryQuantization)

QuantizationConfig = _reflection.GeneratedProtocolMessageType('QuantizationConfig', (_message.Message,), {
  'DESCRIPTOR' : _QUANTIZATIONCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QuantizationConfig)
  })
_sym_db.RegisterMessage(QuantizationConfig)

Disabled = _reflection.GeneratedProtocolMessageType('Disabled', (_message.Message,), {
  'DESCRIPTOR' : _DISABLED,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Disabled)
  })
_sym_db.RegisterMessage(Disabled)

QuantizationConfigDiff = _reflection.GeneratedProtocolMessageType('QuantizationConfigDiff', (_message.Message,), {
  'DESCRIPTOR' : _QUANTIZATIONCONFIGDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.QuantizationConfigDiff)
  })
_sym_db.RegisterMessage(QuantizationConfigDiff)

StrictModeConfig = _reflection.GeneratedProtocolMessageType('StrictModeConfig', (_message.Message,), {
  'DESCRIPTOR' : _STRICTMODECONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StrictModeConfig)
  })
_sym_db.RegisterMessage(StrictModeConfig)

StrictModeSparseConfig = _reflection.GeneratedProtocolMessageType('StrictModeSparseConfig', (_message.Message,), {

  'SparseConfigEntry' : _reflection.GeneratedProtocolMessageType('SparseConfigEntry', (_message.Message,), {
    'DESCRIPTOR' : _STRICTMODESPARSECONFIG_SPARSECONFIGENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.StrictModeSparseConfig.SparseConfigEntry)
    })
  ,
  'DESCRIPTOR' : _STRICTMODESPARSECONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StrictModeSparseConfig)
  })
_sym_db.RegisterMessage(StrictModeSparseConfig)
_sym_db.RegisterMessage(StrictModeSparseConfig.SparseConfigEntry)

StrictModeSparse = _reflection.GeneratedProtocolMessageType('StrictModeSparse', (_message.Message,), {
  'DESCRIPTOR' : _STRICTMODESPARSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StrictModeSparse)
  })
_sym_db.RegisterMessage(StrictModeSparse)

StrictModeMultivectorConfig = _reflection.GeneratedProtocolMessageType('StrictModeMultivectorConfig', (_message.Message,), {

  'MultivectorConfigEntry' : _reflection.GeneratedProtocolMessageType('MultivectorConfigEntry', (_message.Message,), {
    'DESCRIPTOR' : _STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.StrictModeMultivectorConfig.MultivectorConfigEntry)
    })
  ,
  'DESCRIPTOR' : _STRICTMODEMULTIVECTORCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StrictModeMultivectorConfig)
  })
_sym_db.RegisterMessage(StrictModeMultivectorConfig)
_sym_db.RegisterMessage(StrictModeMultivectorConfig.MultivectorConfigEntry)

StrictModeMultivector = _reflection.GeneratedProtocolMessageType('StrictModeMultivector', (_message.Message,), {
  'DESCRIPTOR' : _STRICTMODEMULTIVECTOR,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.StrictModeMultivector)
  })
_sym_db.RegisterMessage(StrictModeMultivector)

CreateCollection = _reflection.GeneratedProtocolMessageType('CreateCollection', (_message.Message,), {
  'DESCRIPTOR' : _CREATECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateCollection)
  })
_sym_db.RegisterMessage(CreateCollection)

UpdateCollection = _reflection.GeneratedProtocolMessageType('UpdateCollection', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollection)
  })
_sym_db.RegisterMessage(UpdateCollection)

DeleteCollection = _reflection.GeneratedProtocolMessageType('DeleteCollection', (_message.Message,), {
  'DESCRIPTOR' : _DELETECOLLECTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteCollection)
  })
_sym_db.RegisterMessage(DeleteCollection)

CollectionOperationResponse = _reflection.GeneratedProtocolMessageType('CollectionOperationResponse', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONOPERATIONRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionOperationResponse)
  })
_sym_db.RegisterMessage(CollectionOperationResponse)

CollectionParams = _reflection.GeneratedProtocolMessageType('CollectionParams', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionParams)
  })
_sym_db.RegisterMessage(CollectionParams)

CollectionParamsDiff = _reflection.GeneratedProtocolMessageType('CollectionParamsDiff', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONPARAMSDIFF,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionParamsDiff)
  })
_sym_db.RegisterMessage(CollectionParamsDiff)

CollectionConfig = _reflection.GeneratedProtocolMessageType('CollectionConfig', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCONFIG,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionConfig)
  })
_sym_db.RegisterMessage(CollectionConfig)

KeywordIndexParams = _reflection.GeneratedProtocolMessageType('KeywordIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _KEYWORDINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.KeywordIndexParams)
  })
_sym_db.RegisterMessage(KeywordIndexParams)

IntegerIndexParams = _reflection.GeneratedProtocolMessageType('IntegerIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _INTEGERINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.IntegerIndexParams)
  })
_sym_db.RegisterMessage(IntegerIndexParams)

FloatIndexParams = _reflection.GeneratedProtocolMessageType('FloatIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _FLOATINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.FloatIndexParams)
  })
_sym_db.RegisterMessage(FloatIndexParams)

GeoIndexParams = _reflection.GeneratedProtocolMessageType('GeoIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _GEOINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.GeoIndexParams)
  })
_sym_db.RegisterMessage(GeoIndexParams)

TextIndexParams = _reflection.GeneratedProtocolMessageType('TextIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _TEXTINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.TextIndexParams)
  })
_sym_db.RegisterMessage(TextIndexParams)

BoolIndexParams = _reflection.GeneratedProtocolMessageType('BoolIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _BOOLINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.BoolIndexParams)
  })
_sym_db.RegisterMessage(BoolIndexParams)

DatetimeIndexParams = _reflection.GeneratedProtocolMessageType('DatetimeIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _DATETIMEINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DatetimeIndexParams)
  })
_sym_db.RegisterMessage(DatetimeIndexParams)

UuidIndexParams = _reflection.GeneratedProtocolMessageType('UuidIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _UUIDINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UuidIndexParams)
  })
_sym_db.RegisterMessage(UuidIndexParams)

PayloadIndexParams = _reflection.GeneratedProtocolMessageType('PayloadIndexParams', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADINDEXPARAMS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadIndexParams)
  })
_sym_db.RegisterMessage(PayloadIndexParams)

PayloadSchemaInfo = _reflection.GeneratedProtocolMessageType('PayloadSchemaInfo', (_message.Message,), {
  'DESCRIPTOR' : _PAYLOADSCHEMAINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.PayloadSchemaInfo)
  })
_sym_db.RegisterMessage(PayloadSchemaInfo)

CollectionInfo = _reflection.GeneratedProtocolMessageType('CollectionInfo', (_message.Message,), {

  'PayloadSchemaEntry' : _reflection.GeneratedProtocolMessageType('PayloadSchemaEntry', (_message.Message,), {
    'DESCRIPTOR' : _COLLECTIONINFO_PAYLOADSCHEMAENTRY,
    '__module__' : 'collections_pb2'
    # @@protoc_insertion_point(class_scope:qdrant.CollectionInfo.PayloadSchemaEntry)
    })
  ,
  'DESCRIPTOR' : _COLLECTIONINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionInfo)
  })
_sym_db.RegisterMessage(CollectionInfo)
_sym_db.RegisterMessage(CollectionInfo.PayloadSchemaEntry)

ChangeAliases = _reflection.GeneratedProtocolMessageType('ChangeAliases', (_message.Message,), {
  'DESCRIPTOR' : _CHANGEALIASES,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ChangeAliases)
  })
_sym_db.RegisterMessage(ChangeAliases)

AliasOperations = _reflection.GeneratedProtocolMessageType('AliasOperations', (_message.Message,), {
  'DESCRIPTOR' : _ALIASOPERATIONS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.AliasOperations)
  })
_sym_db.RegisterMessage(AliasOperations)

CreateAlias = _reflection.GeneratedProtocolMessageType('CreateAlias', (_message.Message,), {
  'DESCRIPTOR' : _CREATEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateAlias)
  })
_sym_db.RegisterMessage(CreateAlias)

RenameAlias = _reflection.GeneratedProtocolMessageType('RenameAlias', (_message.Message,), {
  'DESCRIPTOR' : _RENAMEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RenameAlias)
  })
_sym_db.RegisterMessage(RenameAlias)

DeleteAlias = _reflection.GeneratedProtocolMessageType('DeleteAlias', (_message.Message,), {
  'DESCRIPTOR' : _DELETEALIAS,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteAlias)
  })
_sym_db.RegisterMessage(DeleteAlias)

ListAliasesRequest = _reflection.GeneratedProtocolMessageType('ListAliasesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTALIASESREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListAliasesRequest)
  })
_sym_db.RegisterMessage(ListAliasesRequest)

ListCollectionAliasesRequest = _reflection.GeneratedProtocolMessageType('ListCollectionAliasesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTCOLLECTIONALIASESREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListCollectionAliasesRequest)
  })
_sym_db.RegisterMessage(ListCollectionAliasesRequest)

AliasDescription = _reflection.GeneratedProtocolMessageType('AliasDescription', (_message.Message,), {
  'DESCRIPTOR' : _ALIASDESCRIPTION,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.AliasDescription)
  })
_sym_db.RegisterMessage(AliasDescription)

ListAliasesResponse = _reflection.GeneratedProtocolMessageType('ListAliasesResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTALIASESRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ListAliasesResponse)
  })
_sym_db.RegisterMessage(ListAliasesResponse)

CollectionClusterInfoRequest = _reflection.GeneratedProtocolMessageType('CollectionClusterInfoRequest', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCLUSTERINFOREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionClusterInfoRequest)
  })
_sym_db.RegisterMessage(CollectionClusterInfoRequest)

ShardKey = _reflection.GeneratedProtocolMessageType('ShardKey', (_message.Message,), {
  'DESCRIPTOR' : _SHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ShardKey)
  })
_sym_db.RegisterMessage(ShardKey)

LocalShardInfo = _reflection.GeneratedProtocolMessageType('LocalShardInfo', (_message.Message,), {
  'DESCRIPTOR' : _LOCALSHARDINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.LocalShardInfo)
  })
_sym_db.RegisterMessage(LocalShardInfo)

RemoteShardInfo = _reflection.GeneratedProtocolMessageType('RemoteShardInfo', (_message.Message,), {
  'DESCRIPTOR' : _REMOTESHARDINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RemoteShardInfo)
  })
_sym_db.RegisterMessage(RemoteShardInfo)

ShardTransferInfo = _reflection.GeneratedProtocolMessageType('ShardTransferInfo', (_message.Message,), {
  'DESCRIPTOR' : _SHARDTRANSFERINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ShardTransferInfo)
  })
_sym_db.RegisterMessage(ShardTransferInfo)

ReshardingInfo = _reflection.GeneratedProtocolMessageType('ReshardingInfo', (_message.Message,), {
  'DESCRIPTOR' : _RESHARDINGINFO,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ReshardingInfo)
  })
_sym_db.RegisterMessage(ReshardingInfo)

CollectionClusterInfoResponse = _reflection.GeneratedProtocolMessageType('CollectionClusterInfoResponse', (_message.Message,), {
  'DESCRIPTOR' : _COLLECTIONCLUSTERINFORESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CollectionClusterInfoResponse)
  })
_sym_db.RegisterMessage(CollectionClusterInfoResponse)

MoveShard = _reflection.GeneratedProtocolMessageType('MoveShard', (_message.Message,), {
  'DESCRIPTOR' : _MOVESHARD,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.MoveShard)
  })
_sym_db.RegisterMessage(MoveShard)

ReplicateShard = _reflection.GeneratedProtocolMessageType('ReplicateShard', (_message.Message,), {
  'DESCRIPTOR' : _REPLICATESHARD,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.ReplicateShard)
  })
_sym_db.RegisterMessage(ReplicateShard)

AbortShardTransfer = _reflection.GeneratedProtocolMessageType('AbortShardTransfer', (_message.Message,), {
  'DESCRIPTOR' : _ABORTSHARDTRANSFER,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.AbortShardTransfer)
  })
_sym_db.RegisterMessage(AbortShardTransfer)

RestartTransfer = _reflection.GeneratedProtocolMessageType('RestartTransfer', (_message.Message,), {
  'DESCRIPTOR' : _RESTARTTRANSFER,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.RestartTransfer)
  })
_sym_db.RegisterMessage(RestartTransfer)

Replica = _reflection.GeneratedProtocolMessageType('Replica', (_message.Message,), {
  'DESCRIPTOR' : _REPLICA,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.Replica)
  })
_sym_db.RegisterMessage(Replica)

CreateShardKey = _reflection.GeneratedProtocolMessageType('CreateShardKey', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKey)
  })
_sym_db.RegisterMessage(CreateShardKey)

DeleteShardKey = _reflection.GeneratedProtocolMessageType('DeleteShardKey', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEY,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKey)
  })
_sym_db.RegisterMessage(DeleteShardKey)

UpdateCollectionClusterSetupRequest = _reflection.GeneratedProtocolMessageType('UpdateCollectionClusterSetupRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTIONCLUSTERSETUPREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollectionClusterSetupRequest)
  })
_sym_db.RegisterMessage(UpdateCollectionClusterSetupRequest)

UpdateCollectionClusterSetupResponse = _reflection.GeneratedProtocolMessageType('UpdateCollectionClusterSetupResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATECOLLECTIONCLUSTERSETUPRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.UpdateCollectionClusterSetupResponse)
  })
_sym_db.RegisterMessage(UpdateCollectionClusterSetupResponse)

CreateShardKeyRequest = _reflection.GeneratedProtocolMessageType('CreateShardKeyRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEYREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKeyRequest)
  })
_sym_db.RegisterMessage(CreateShardKeyRequest)

DeleteShardKeyRequest = _reflection.GeneratedProtocolMessageType('DeleteShardKeyRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEYREQUEST,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKeyRequest)
  })
_sym_db.RegisterMessage(DeleteShardKeyRequest)

CreateShardKeyResponse = _reflection.GeneratedProtocolMessageType('CreateShardKeyResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATESHARDKEYRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.CreateShardKeyResponse)
  })
_sym_db.RegisterMessage(CreateShardKeyResponse)

DeleteShardKeyResponse = _reflection.GeneratedProtocolMessageType('DeleteShardKeyResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETESHARDKEYRESPONSE,
  '__module__' : 'collections_pb2'
  # @@protoc_insertion_point(class_scope:qdrant.DeleteShardKeyResponse)
  })
_sym_db.RegisterMessage(DeleteShardKeyResponse)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\252\002\022Qdrant.Client.Grpc'
  _VECTORPARAMSMAP_MAPENTRY._options = None
  _VECTORPARAMSMAP_MAPENTRY._serialized_options = b'8\001'
  _VECTORPARAMSDIFFMAP_MAPENTRY._options = None
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_options = b'8\001'
  _SPARSEVECTORCONFIG_MAPENTRY._options = None
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_options = b'8\001'
  _STRICTMODESPARSECONFIG_SPARSECONFIGENTRY._options = None
  _STRICTMODESPARSECONFIG_SPARSECONFIGENTRY._serialized_options = b'8\001'
  _STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY._options = None
  _STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY._serialized_options = b'8\001'
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._options = None
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_options = b'8\001'
  _DATATYPE._serialized_start=13995
  _DATATYPE._serialized_end=14055
  _MODIFIER._serialized_start=14057
  _MODIFIER._serialized_end=14086
  _MULTIVECTORCOMPARATOR._serialized_start=14088
  _MULTIVECTORCOMPARATOR._serialized_end=14123
  _DISTANCE._serialized_start=14125
  _DISTANCE._serialized_end=14204
  _COLLECTIONSTATUS._serialized_start=14206
  _COLLECTIONSTATUS._serialized_end=14295
  _PAYLOADSCHEMATYPE._serialized_start=14297
  _PAYLOADSCHEMATYPE._serialized_end=14423
  _QUANTIZATIONTYPE._serialized_start=14425
  _QUANTIZATIONTYPE._serialized_end=14478
  _COMPRESSIONRATIO._serialized_start=14480
  _COMPRESSIONRATIO._serialized_end=14541
  _SHARDINGMETHOD._serialized_start=14543
  _SHARDINGMETHOD._serialized_end=14581
  _TOKENIZERTYPE._serialized_start=14583
  _TOKENIZERTYPE._serialized_end=14667
  _REPLICASTATE._serialized_start=14670
  _REPLICASTATE._serialized_end=14827
  _RESHARDINGDIRECTION._serialized_start=14829
  _RESHARDINGDIRECTION._serialized_end=14868
  _SHARDTRANSFERMETHOD._serialized_start=14870
  _SHARDTRANSFERMETHOD._serialized_end=14967
  _VECTORPARAMS._serialized_start=30
  _VECTORPARAMS._serialized_end=417
  _VECTORPARAMSDIFF._serialized_start=420
  _VECTORPARAMSDIFF._serialized_end=628
  _VECTORPARAMSMAP._serialized_start=631
  _VECTORPARAMSMAP._serialized_end=761
  _VECTORPARAMSMAP_MAPENTRY._serialized_start=697
  _VECTORPARAMSMAP_MAPENTRY._serialized_end=761
  _VECTORPARAMSDIFFMAP._serialized_start=764
  _VECTORPARAMSDIFFMAP._serialized_end=906
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_start=838
  _VECTORPARAMSDIFFMAP_MAPENTRY._serialized_end=906
  _VECTORSCONFIG._serialized_start=908
  _VECTORSCONFIG._serialized_end=1020
  _VECTORSCONFIGDIFF._serialized_start=1022
  _VECTORSCONFIGDIFF._serialized_end=1146
  _SPARSEVECTORPARAMS._serialized_start=1149
  _SPARSEVECTORPARAMS._serialized_end=1280
  _SPARSEVECTORCONFIG._serialized_start=1283
  _SPARSEVECTORCONFIG._serialized_end=1425
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_start=1355
  _SPARSEVECTORCONFIG_MAPENTRY._serialized_end=1425
  _MULTIVECTORCONFIG._serialized_start=1427
  _MULTIVECTORCONFIG._serialized_end=1497
  _GETCOLLECTIONINFOREQUEST._serialized_start=1499
  _GETCOLLECTIONINFOREQUEST._serialized_end=1550
  _COLLECTIONEXISTSREQUEST._serialized_start=1552
  _COLLECTIONEXISTSREQUEST._serialized_end=1602
  _COLLECTIONEXISTS._serialized_start=1604
  _COLLECTIONEXISTS._serialized_end=1638
  _COLLECTIONEXISTSRESPONSE._serialized_start=1640
  _COLLECTIONEXISTSRESPONSE._serialized_end=1722
  _LISTCOLLECTIONSREQUEST._serialized_start=1724
  _LISTCOLLECTIONSREQUEST._serialized_end=1748
  _COLLECTIONDESCRIPTION._serialized_start=1750
  _COLLECTIONDESCRIPTION._serialized_end=1787
  _GETCOLLECTIONINFORESPONSE._serialized_start=1789
  _GETCOLLECTIONINFORESPONSE._serialized_end=1870
  _LISTCOLLECTIONSRESPONSE._serialized_start=1872
  _LISTCOLLECTIONSRESPONSE._serialized_end=1963
  _MAXOPTIMIZATIONTHREADS._serialized_start=1966
  _MAXOPTIMIZATIONTHREADS._serialized_end=2098
  _MAXOPTIMIZATIONTHREADS_SETTING._serialized_start=2068
  _MAXOPTIMIZATIONTHREADS_SETTING._serialized_end=2087
  _OPTIMIZERSTATUS._serialized_start=2100
  _OPTIMIZERSTATUS._serialized_end=2144
  _HNSWCONFIGDIFF._serialized_start=2147
  _HNSWCONFIGDIFF._serialized_end=2419
  _SPARSEINDEXCONFIG._serialized_start=2422
  _SPARSEINDEXCONFIG._serialized_end=2587
  _WALCONFIGDIFF._serialized_start=2589
  _WALCONFIGDIFF._serialized_end=2710
  _OPTIMIZERSCONFIGDIFF._serialized_start=2713
  _OPTIMIZERSCONFIGDIFF._serialized_end=3327
  _SCALARQUANTIZATION._serialized_start=3330
  _SCALARQUANTIZATION._serialized_end=3466
  _PRODUCTQUANTIZATION._serialized_start=3468
  _PRODUCTQUANTIZATION._serialized_end=3576
  _BINARYQUANTIZATION._serialized_start=3578
  _BINARYQUANTIZATION._serialized_end=3638
  _QUANTIZATIONCONFIG._serialized_start=3641
  _QUANTIZATIONCONFIG._serialized_end=3817
  _DISABLED._serialized_start=3819
  _DISABLED._serialized_end=3829
  _QUANTIZATIONCONFIGDIFF._serialized_start=3832
  _QUANTIZATIONCONFIGDIFF._serialized_end=4050
  _STRICTMODECONFIG._serialized_start=4053
  _STRICTMODECONFIG._serialized_end=5196
  _STRICTMODESPARSECONFIG._serialized_start=5199
  _STRICTMODESPARSECONFIG._serialized_end=5375
  _STRICTMODESPARSECONFIG_SPARSECONFIGENTRY._serialized_start=5298
  _STRICTMODESPARSECONFIG_SPARSECONFIGENTRY._serialized_end=5375
  _STRICTMODESPARSE._serialized_start=5377
  _STRICTMODESPARSE._serialized_end=5435
  _STRICTMODEMULTIVECTORCONFIG._serialized_start=5438
  _STRICTMODEMULTIVECTORCONFIG._serialized_end=5644
  _STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY._serialized_start=5557
  _STRICTMODEMULTIVECTORCONFIG_MULTIVECTORCONFIGENTRY._serialized_end=5644
  _STRICTMODEMULTIVECTOR._serialized_start=5646
  _STRICTMODEMULTIVECTOR._serialized_end=5711
  _CREATECOLLECTION._serialized_start=5714
  _CREATECOLLECTION._serialized_end=6697
  _UPDATECOLLECTION._serialized_start=6700
  _UPDATECOLLECTION._serialized_end=7326
  _DELETECOLLECTION._serialized_start=7328
  _DELETECOLLECTION._serialized_end=7405
  _COLLECTIONOPERATIONRESPONSE._serialized_start=7407
  _COLLECTIONOPERATIONRESPONSE._serialized_end=7466
  _COLLECTIONPARAMS._serialized_start=7469
  _COLLECTIONPARAMS._serialized_end=7963
  _COLLECTIONPARAMSDIFF._serialized_start=7966
  _COLLECTIONPARAMSDIFF._serialized_end=8220
  _COLLECTIONCONFIG._serialized_start=8223
  _COLLECTIONCONFIG._serialized_end=8595
  _KEYWORDINDEXPARAMS._serialized_start=8597
  _KEYWORDINDEXPARAMS._serialized_end=8689
  _INTEGERINDEXPARAMS._serialized_start=8692
  _INTEGERINDEXPARAMS._serialized_end=8852
  _FLOATINDEXPARAMS._serialized_start=8854
  _FLOATINDEXPARAMS._serialized_end=8950
  _GEOINDEXPARAMS._serialized_start=8952
  _GEOINDEXPARAMS._serialized_end=9002
  _TEXTINDEXPARAMS._serialized_start=9005
  _TEXTINDEXPARAMS._serialized_end=9228
  _BOOLINDEXPARAMS._serialized_start=9230
  _BOOLINDEXPARAMS._serialized_end=9281
  _DATETIMEINDEXPARAMS._serialized_start=9283
  _DATETIMEINDEXPARAMS._serialized_end=9382
  _UUIDINDEXPARAMS._serialized_start=9384
  _UUIDINDEXPARAMS._serialized_end=9473
  _PAYLOADINDEXPARAMS._serialized_start=9476
  _PAYLOADINDEXPARAMS._serialized_end=9964
  _PAYLOADSCHEMAINFO._serialized_start=9967
  _PAYLOADSCHEMAINFO._serialized_end=10124
  _COLLECTIONINFO._serialized_start=10127
  _COLLECTIONINFO._serialized_end=10614
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_start=10462
  _COLLECTIONINFO_PAYLOADSCHEMAENTRY._serialized_end=10541
  _CHANGEALIASES._serialized_start=10616
  _CHANGEALIASES._serialized_end=10707
  _ALIASOPERATIONS._serialized_start=10710
  _ALIASOPERATIONS._serialized_end=10872
  _CREATEALIAS._serialized_start=10874
  _CREATEALIAS._serialized_end=10932
  _RENAMEALIAS._serialized_start=10934
  _RENAMEALIAS._serialized_end=10995
  _DELETEALIAS._serialized_start=10997
  _DELETEALIAS._serialized_end=11030
  _LISTALIASESREQUEST._serialized_start=11032
  _LISTALIASESREQUEST._serialized_end=11052
  _LISTCOLLECTIONALIASESREQUEST._serialized_start=11054
  _LISTCOLLECTIONALIASESREQUEST._serialized_end=11109
  _ALIASDESCRIPTION._serialized_start=11111
  _ALIASDESCRIPTION._serialized_end=11174
  _LISTALIASESRESPONSE._serialized_start=11176
  _LISTALIASESRESPONSE._serialized_end=11254
  _COLLECTIONCLUSTERINFOREQUEST._serialized_start=11256
  _COLLECTIONCLUSTERINFOREQUEST._serialized_end=11311
  _SHARDKEY._serialized_start=11313
  _SHARDKEY._serialized_end=11367
  _LOCALSHARDINFO._serialized_start=11370
  _LOCALSHARDINFO._serialized_end=11519
  _REMOTESHARDINFO._serialized_start=11522
  _REMOTESHARDINFO._serialized_end=11667
  _SHARDTRANSFERINFO._serialized_start=11669
  _SHARDTRANSFERINFO._serialized_end=11788
  _RESHARDINGINFO._serialized_start=11791
  _RESHARDINGINFO._serialized_end=11946
  _COLLECTIONCLUSTERINFORESPONSE._serialized_start=11949
  _COLLECTIONCLUSTERINFORESPONSE._serialized_end=12219
  _MOVESHARD._serialized_start=12222
  _MOVESHARD._serialized_end=12396
  _REPLICATESHARD._serialized_start=12399
  _REPLICATESHARD._serialized_end=12578
  _ABORTSHARDTRANSFER._serialized_start=12580
  _ABORTSHARDTRANSFER._serialized_end=12702
  _RESTARTTRANSFER._serialized_start=12705
  _RESTARTTRANSFER._serialized_end=12869
  _REPLICA._serialized_start=12871
  _REPLICA._serialized_end=12915
  _CREATESHARDKEY._serialized_start=12918
  _CREATESHARDKEY._serialized_end=13092
  _DELETESHARDKEY._serialized_start=13094
  _DELETESHARDKEY._serialized_end=13147
  _UPDATECOLLECTIONCLUSTERSETUPREQUEST._serialized_start=13150
  _UPDATECOLLECTIONCLUSTERSETUPREQUEST._serialized_end=13603
  _UPDATECOLLECTIONCLUSTERSETUPRESPONSE._serialized_start=13605
  _UPDATECOLLECTIONCLUSTERSETUPRESPONSE._serialized_end=13659
  _CREATESHARDKEYREQUEST._serialized_start=13661
  _CREATESHARDKEYREQUEST._serialized_end=13784
  _DELETESHARDKEYREQUEST._serialized_start=13786
  _DELETESHARDKEYREQUEST._serialized_end=13909
  _CREATESHARDKEYRESPONSE._serialized_start=13911
  _CREATESHARDKEYRESPONSE._serialized_end=13951
  _DELETESHARDKEYRESPONSE._serialized_start=13953
  _DELETESHARDKEYRESPONSE._serialized_end=13993
# @@protoc_insertion_point(module_scope)
