#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/log_sigmoid_ops.h>

namespace at {


// aten::log_sigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log_sigmoid_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::log_sigmoid_out::call(self, out);
}
// aten::log_sigmoid.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log_sigmoid_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::log_sigmoid_out::call(self, out);
}

// aten::log_sigmoid(Tensor self) -> Tensor
inline at::Tensor log_sigmoid(const at::Tensor & self) {
    return at::_ops::log_sigmoid::call(self);
}

}
