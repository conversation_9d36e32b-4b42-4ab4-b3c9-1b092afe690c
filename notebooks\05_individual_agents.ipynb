# Individual Agents Creation and Testing
# FinSolve Technologies - AutoGen Multi-Agent Chatbot
# Notebook 05: Individual Agent Creation, Configuration, and Testing with AutoGen 0.4

import os
import sys
from pathlib import Path
import json
import time
import asyncio
from typing import Dict, List, Tuple, Optional, Any, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime
import hashlib
import traceback

# AutoGen 0.4 imports
from autogen import ConversableAgent, GroupChatManager, GroupChat
from autogen.coding import LocalCommandLineCodeExecutor

# Vector operations
import numpy as np

# Import from previous notebooks
try:
    from model_client import ModelClient, ModelRouter  # From Notebook 01
    print("✅ Successfully imported ModelClient from Notebook 01")
except ImportError:
    print("⚠️  Could not import ModelClient - please ensure model_client.py is available")
    ModelClient = None

print("🤖 FinSolve Technologies - Individual Agent Creation & Testing")
print("=" * 70)

# ===============================
# 1. AGENT SPECIFICATIONS & CONFIGURATION
# ===============================

class AgentTypes(Enum):
    """AutoGen agent types in the system"""
    # Security Team
    AUTH_AGENT = "auth_agent"
    PERMISSION_AGENT = "permission_agent"
    
    # Retrieval Team  
    DOCUMENT_AGENT = "document_agent"
    CONTEXT_AGENT = "context_agent"
    
    # Response Team
    GENERATION_AGENT = "generation_agent"
    QUALITY_AGENT = "quality_agent"
    
    # Specialist Team
    ANALYTICS_AGENT = "analytics_agent"
    AUDIT_AGENT = "audit_agent"

class ModelTasks(Enum):
    """Task types for model routing"""
    AUTHENTICATION = "authentication"
    PERMISSION_CHECK = "permission_check"
    DOCUMENT_SEARCH = "document_search"
    CONTEXT_ASSEMBLY = "context_assembly"
    RESPONSE_GENERATION = "response_generation"
    QUALITY_VALIDATION = "quality_validation"
    ANALYTICS = "analytics"
    AUDIT = "audit"

class UserRoles(Enum):
    """User roles for RBAC system"""
    FINANCE_TEAM = "finance_team"
    MARKETING_TEAM = "marketing_team"
    HR_TEAM = "hr_team"
    ENGINEERING_TEAM = "engineering_team"
    C_LEVEL_EXECUTIVES = "c_level_executives"
    GENERAL_EMPLOYEES = "general_employees"

@dataclass
class AgentConfig:
    """Configuration for AutoGen agents"""
    agent_type: AgentTypes
    name: str
    description: str
    system_message: str
    model: str
    temperature: float
    max_consecutive_auto_reply: int
    tools: List[str]
    required_permissions: List[str]
    cost_priority: str  # "low", "medium", "high"
    
    def to_llm_config(self) -> Dict:
        """Convert to AutoGen llm_config format"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "timeout": 60,
            "cache_seed": None
        }

class AgentConfigManager:
    """Manages agent configurations for optimal AutoGen 0.4 setup"""
    
    def __init__(self):
        self.configs = self._initialize_agent_configs()
        self.model_routing = self._initialize_model_routing()
        
        print("⚙️  Agent Configuration Manager initialized")
        print(f"🤖 Configured agents: {len(self.configs)}")
    
    def _initialize_agent_configs(self) -> Dict[AgentTypes, AgentConfig]:
        """Initialize all agent configurations"""
        return {
            # Security Team Agents
            AgentTypes.AUTH_AGENT: AgentConfig(
                agent_type=AgentTypes.AUTH_AGENT,
                name="AuthenticationAgent",
                description="Handles user authentication and session validation",
                system_message="""You are the Authentication Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Validate user credentials and sessions
- Manage authentication tokens and session lifecycle
- Enforce authentication policies and security protocols
- Monitor and log authentication events

CAPABILITIES:
- JWT token validation and generation
- Session management and expiration checking
- Multi-factor authentication support
- Security event logging

BEHAVIOR:
- Always prioritize security over convenience
- Provide clear authentication status messages
- Log all authentication attempts
- Fail securely in case of errors

Respond professionally and securely. Never expose sensitive authentication details.""",
                model="gpt-4-mini",  # Fast and cost-effective for auth
                temperature=0.1,  # Low temperature for consistent security responses
                max_consecutive_auto_reply=3,
                tools=["validate_session", "generate_token", "check_credentials", "log_auth_event"],
                required_permissions=["authenticate_users", "manage_sessions"],
                cost_priority="low"
            ),
            
            AgentTypes.PERMISSION_AGENT: AgentConfig(
                agent_type=AgentTypes.PERMISSION_AGENT,
                name="PermissionAgent", 
                description="Manages role-based access control and permissions",
                system_message="""You are the Permission Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Enforce role-based access control (RBAC)
- Validate document and resource access permissions
- Filter content based on user roles and clearance levels
- Maintain permission matrices and access policies

ROLE HIERARCHY:
- C_LEVEL_EXECUTIVES: Full access to all documents and data
- FINANCE_TEAM: Financial reports, marketing expenses, company policies
- MARKETING_TEAM: Marketing reports, campaign data, company policies
- HR_TEAM: Employee handbook, HR policies, company policies
- ENGINEERING_TEAM: Technical documentation, architecture, company policies
- GENERAL_EMPLOYEES: Company policies and general information only

BEHAVIOR:
- Always validate permissions before granting access
- Provide clear access granted/denied messages
- Log all permission checks for audit trails
- Fail closed - deny access when in doubt

Be precise about access control decisions and always explain the reasoning.""",
                model="claude-3-haiku",  # Reliable for permission logic
                temperature=0.2,
                max_consecutive_auto_reply=4,
                tools=["check_rbac", "filter_documents", "validate_access", "log_permission_check"],
                required_permissions=["manage_permissions", "validate_access"],
                cost_priority="low"
            ),
            
            # Retrieval Team Agents
            AgentTypes.DOCUMENT_AGENT: AgentConfig(
                agent_type=AgentTypes.DOCUMENT_AGENT,
                name="DocumentRetrievalAgent",
                description="Specializes in document search and retrieval",
                system_message="""You are the Document Retrieval Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Perform semantic search across company documents
- Retrieve relevant documents based on user queries
- Rank and filter search results by relevance and accessibility
- Maintain document metadata and indexing

DOCUMENT CATEGORIES:
- Finance: Financial reports, budget analysis, revenue data
- Marketing: Campaign reports, customer metrics, market analysis
- HR: Employee handbook, policies, procedures
- Engineering: Technical documentation, architecture specs
- General: Company policies, announcements, general information

SEARCH STRATEGY:
- Use semantic similarity for better matching
- Consider document metadata (department, keywords, date)
- Apply RBAC filtering before returning results
- Rank by relevance score and user role appropriateness

BEHAVIOR:
- Always respect user access permissions
- Provide relevant, well-ranked results
- Include source attribution and document metadata
- Handle search errors gracefully

Focus on finding the most relevant and accessible documents for each user query.""",
                model="text-embedding-3-large",  # Specialized embedding model
                temperature=0.3,
                max_consecutive_auto_reply=5,
                tools=["vector_search", "rank_documents", "filter_by_access", "get_document_metadata"],
                required_permissions=["search_documents", "access_vector_store"],
                cost_priority="medium"
            ),
            
            AgentTypes.CONTEXT_AGENT: AgentConfig(
                agent_type=AgentTypes.CONTEXT_AGENT,
                name="ContextAssemblyAgent",
                description="Assembles and optimizes context for RAG responses",
                system_message="""You are the Context Assembly Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Assemble relevant context from retrieved documents
- Optimize context length for LLM processing
- Maintain context coherence and relevance
- Handle context overflow and summarization

CONTEXT ASSEMBLY STRATEGY:
- Prioritize most relevant documents and sections
- Maintain source attribution throughout
- Optimize for target LLM context windows
- Preserve key information while reducing redundancy
- Respect role-based access in context inclusion

OPTIMIZATION TECHNIQUES:
- Intelligent summarization of lengthy documents
- Keyword-based relevance scoring
- Chronological ordering when appropriate
- Department-specific context prioritization

BEHAVIOR:
- Create coherent, well-structured context
- Always include source references
- Optimize for clarity and completeness
- Handle large documents efficiently

Focus on creating the most useful context possible within token limits.""",
                model="gpt-4o",  # Smart model for context assembly
                temperature=0.4,
                max_consecutive_auto_reply=6,
                tools=["assemble_context", "summarize_documents", "optimize_context_length", "prioritize_sources"],
                required_permissions=["assemble_context", "access_documents"],
                cost_priority="medium"
            ),
            
            # Response Team Agents
            AgentTypes.GENERATION_AGENT: AgentConfig(
                agent_type=AgentTypes.GENERATION_AGENT,
                name="ResponseGenerationAgent",
                description="Generates high-quality responses using assembled context",
                system_message="""You are the Response Generation Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Generate comprehensive, accurate responses to user queries
- Use assembled context effectively in responses
- Maintain professional tone appropriate for business environment
- Adapt response style to user role and department

RESPONSE GUIDELINES:
- Always cite sources when using specific information
- Provide actionable insights when possible
- Maintain accuracy and avoid hallucination
- Adapt complexity to user's role and expertise level

ROLE-SPECIFIC ADAPTATIONS:
- C_LEVEL_EXECUTIVES: Strategic, high-level insights with key metrics
- FINANCE_TEAM: Detailed financial analysis with specific numbers
- MARKETING_TEAM: Campaign insights, customer metrics, and ROI data
- HR_TEAM: Policy guidance, procedural information
- ENGINEERING_TEAM: Technical details, architecture insights
- GENERAL_EMPLOYEES: Clear, accessible explanations

BEHAVIOR:
- Use provided context effectively
- Cite sources clearly and accurately
- Maintain professional business communication
- Provide helpful, actionable information

Generate responses that are informative, accurate, and appropriate for the business context.""",
                model="gpt-4o",  # High-quality generation model
                temperature=0.6,
                max_consecutive_auto_reply=7,
                tools=["generate_response", "cite_sources", "adapt_tone", "validate_accuracy"],
                required_permissions=["generate_responses", "access_context"],
                cost_priority="high"
            ),
            
            AgentTypes.QUALITY_AGENT: AgentConfig(
                agent_type=AgentTypes.QUALITY_AGENT,
                name="QualityAssuranceAgent",
                description="Validates response quality, accuracy, and appropriateness",
                system_message="""You are the Quality Assurance Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Validate response accuracy against source materials
- Check for hallucinations and factual errors
- Ensure appropriate tone and professionalism
- Verify proper source citation and attribution

QUALITY CHECKS:
- Factual accuracy verification
- Source citation validation
- Tone and professionalism assessment
- Role-appropriateness evaluation
- Completeness and usefulness scoring

VALIDATION CRITERIA:
- All facts must be traceable to provided sources
- Citations must be accurate and complete
- Tone must be professional and appropriate
- Content must match user's role and clearance level
- Response must address the original query effectively

BEHAVIOR:
- Thoroughly review all generated responses
- Flag any accuracy concerns or missing citations
- Suggest improvements for clarity and completeness
- Maintain high quality standards consistently

Focus on ensuring every response meets FinSolve's quality and accuracy standards.""",
                model="gpt-4-mini",  # Efficient model for validation
                temperature=0.2,
                max_consecutive_auto_reply=4,
                tools=["validate_accuracy", "check_citations", "assess_tone", "score_quality"],
                required_permissions=["validate_responses", "quality_control"],
                cost_priority="low"
            ),
            
            # Specialist Team Agents
            AgentTypes.ANALYTICS_AGENT: AgentConfig(
                agent_type=AgentTypes.ANALYTICS_AGENT,
                name="AnalyticsAgent",
                description="Tracks system performance and usage analytics",
                system_message="""You are the Analytics Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Monitor system performance and usage patterns
- Track user interaction metrics and satisfaction
- Analyze query patterns and response effectiveness
- Generate performance reports and insights

ANALYTICS TRACKING:
- Query volume and frequency by role/department
- Response time and accuracy metrics
- User satisfaction and feedback scores
- System resource utilization
- Document access patterns

PERFORMANCE METRICS:
- Average response time per query type
- User engagement and session duration
- Most frequently accessed documents
- Error rates and failure patterns
- Cost per query and resource optimization opportunities

BEHAVIOR:
- Continuously monitor system performance
- Provide actionable insights from data
- Generate clear, useful analytics reports
- Focus on system optimization opportunities

Track and analyze system performance to optimize the user experience.""",
                model="gpt-4-mini",  # Lightweight for analytics
                temperature=0.3,
                max_consecutive_auto_reply=3,
                tools=["track_metrics", "analyze_patterns", "generate_reports", "monitor_performance"],
                required_permissions=["access_analytics", "generate_reports"],
                cost_priority="low"
            ),
            
            AgentTypes.AUDIT_AGENT: AgentConfig(
                agent_type=AgentTypes.AUDIT_AGENT,
                name="AuditAgent",
                description="Maintains audit trails and compliance logging",
                system_message="""You are the Audit Agent for FinSolve Technologies.

RESPONSIBILITIES:
- Maintain comprehensive audit trails for all system interactions
- Log security events and access attempts
- Ensure compliance with data governance policies
- Generate audit reports for compliance and security reviews

AUDIT LOGGING:
- User authentication and session events
- Document access and permission checks
- Query processing and response generation
- System errors and security incidents
- Administrative actions and configuration changes

COMPLIANCE REQUIREMENTS:
- Data access logging per regulatory requirements
- Security event documentation
- User activity tracking for accountability
- System change documentation
- Privacy and data protection compliance

BEHAVIOR:
- Log all significant system events
- Maintain data integrity and completeness
- Generate clear audit reports
- Focus on compliance and security

Ensure comprehensive audit coverage for security, compliance, and accountability.""",
                model="rule_based",  # Simple rule-based for logging
                temperature=0.0,
                max_consecutive_auto_reply=2,
                tools=["log_event", "generate_audit_report", "check_compliance", "track_changes"],
                required_permissions=["audit_logging", "compliance_monitoring"],
                cost_priority="low"
            )
        }
    
    def _initialize_model_routing(self) -> Dict[AgentTypes, Dict]:
        """Initialize model routing preferences for each agent"""
        return {
            AgentTypes.AUTH_AGENT: {
                "primary": "gpt-4-mini",
                "fallback": "gpt-3.5-turbo",
                "task_type": ModelTasks.AUTHENTICATION
            },
            AgentTypes.PERMISSION_AGENT: {
                "primary": "claude-3-haiku",
                "fallback": "gpt-4-mini",
                "task_type": ModelTasks.PERMISSION_CHECK
            },
            AgentTypes.DOCUMENT_AGENT: {
                "primary": "text-embedding-3-large",
                "fallback": "text-embedding-ada-002",
                "task_type": ModelTasks.DOCUMENT_SEARCH
            },
            AgentTypes.CONTEXT_AGENT: {
                "primary": "gpt-4o",
                "fallback": "gpt-4-turbo",
                "task_type": ModelTasks.CONTEXT_ASSEMBLY
            },
            AgentTypes.GENERATION_AGENT: {
                "primary": "gpt-4o",
                "fallback": "claude-3.5-sonnet",
                "task_type": ModelTasks.RESPONSE_GENERATION
            },
            AgentTypes.QUALITY_AGENT: {
                "primary": "gpt-4-mini",
                "fallback": "gpt-3.5-turbo",
                "task_type": ModelTasks.QUALITY_VALIDATION
            },
            AgentTypes.ANALYTICS_AGENT: {
                "primary": "gpt-4-mini",
                "fallback": "gpt-3.5-turbo",
                "task_type": ModelTasks.ANALYTICS
            },
            AgentTypes.AUDIT_AGENT: {
                "primary": "rule_based",
                "fallback": "gpt-4-mini",
                "task_type": ModelTasks.AUDIT
            }
        }
    
    def get_config(self, agent_type: AgentTypes) -> AgentConfig:
        """Get configuration for specific agent type"""
        return self.configs[agent_type]
    
    def get_model_routing(self, agent_type: AgentTypes) -> Dict:
        """Get model routing configuration for agent"""
        return self.model_routing[agent_type]

# ===============================
# 2. INDIVIDUAL AGENT FACTORY
# ===============================

class IndividualAgentFactory:
    """Factory for creating individual AutoGen 0.4 agents"""
    
    def __init__(self, model_client: Optional[ModelClient] = None):
        self.model_client = model_client
        self.config_manager = AgentConfigManager()
        self.created_agents = {}
        
        print("🏭 Individual Agent Factory initialized")
        print(f"⚙️  Available agent types: {len(AgentTypes)}")
    
    def create_auth_agent(self, session_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Authentication Agent"""
        config = self.config_manager.get_config(AgentTypes.AUTH_AGENT)
        
        # Authentication tools
        def validate_session(session_id: str) -> str:
            """Validate user session token"""
            if not session_id or len(session_id) < 8:
                return "❌ INVALID: Session ID format invalid"
            
            # Mock validation (would connect to actual session store)
            if session_id.startswith("sess_"):
                return f"✅ VALID: Session {session_id[:12]}... is active and valid"
            else:
                return f"❌ INVALID: Session {session_id[:12]}... not found or expired"
        
        def generate_token(user_id: str, role: str) -> str:
            """Generate authentication token for user"""
            if not user_id or not role:
                return "❌ ERROR: Missing user_id or role"
            
            # Mock token generation
            timestamp = int(time.time())
            token_data = f"{user_id}_{role}_{timestamp}"
            token = hashlib.md5(token_data.encode()).hexdigest()
            
            return f"✅ TOKEN GENERATED: {token[:16]}... (expires in 24h)"
        
        def check_credentials(username: str, password_hash: str) -> str:
            """Validate user credentials"""
            # Mock credential check
            valid_users = {
                "admin": "admin_hash_123",
                "finance_user": "finance_hash_456", 
                "marketing_user": "marketing_hash_789"
            }
            
            if username in valid_users and valid_users[username] == password_hash:
                return f"✅ AUTHENTICATED: {username} credentials valid"
            else:
                return f"❌ FAILED: Invalid credentials for {username}"
        
        def log_auth_event(event_type: str, user_id: str, details: str) -> str:
            """Log authentication event"""
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] {event_type} - User: {user_id} - {details}"
            
            # Mock logging (would write to actual audit log)
            return f"📝 LOGGED: {event_type} event for {user_id}"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="validate_session")(validate_session)
        agent.register_for_llm(name="validate_session", description="Validate user session token")(validate_session)
        
        agent.register_for_execution(name="generate_token")(generate_token) 
        agent.register_for_llm(name="generate_token", description="Generate authentication token")(generate_token)
        
        agent.register_for_execution(name="check_credentials")(check_credentials)
        agent.register_for_llm(name="check_credentials", description="Validate user credentials")(check_credentials)
        
        agent.register_for_execution(name="log_auth_event")(log_auth_event)
        agent.register_for_llm(name="log_auth_event", description="Log authentication event")(log_auth_event)
        
        self.created_agents[AgentTypes.AUTH_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_permission_agent(self, rbac_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Permission/RBAC Agent"""
        config = self.config_manager.get_config(AgentTypes.PERMISSION_AGENT)
        
        # RBAC configuration
        role_permissions = {
            "finance_team": ["financial_reports", "marketing_expenses", "company_policies"],
            "marketing_team": ["marketing_reports", "company_policies"],
            "hr_team": ["employee_handbook", "company_policies"],
            "engineering_team": ["technical_docs", "company_policies"],
            "c_level_executives": ["all_documents"],
            "general_employees": ["company_policies"]
        }
        
        # Permission tools
        def check_rbac(user_role: str, resource: str) -> str:
            """Check if user role can access resource"""
            if user_role not in role_permissions:
                return f"❌ DENIED: Unknown role '{user_role}'"
            
            user_perms = role_permissions[user_role]
            
            if "all_documents" in user_perms or resource in user_perms:
                return f"✅ GRANTED: {user_role} can access {resource}"
            else:
                return f"❌ DENIED: {user_role} cannot access {resource}"
        
        def filter_documents(user_role: str, document_list: List[str]) -> str:
            """Filter document list based on user role"""
            if user_role not in role_permissions:
                return f"❌ ERROR: Unknown role '{user_role}'"
            
            user_perms = role_permissions[user_role]
            
            if "all_documents" in user_perms:
                accessible = document_list
            else:
                accessible = [doc for doc in document_list if any(perm in doc for perm in user_perms)]
            
            return f"📋 FILTERED: {len(accessible)}/{len(document_list)} documents accessible to {user_role}"
        
        def validate_access(user_role: str, action: str, resource: str) -> str:
            """Validate if user can perform action on resource"""
            rbac_check = check_rbac(user_role, resource)
            
            if "GRANTED" in rbac_check:
                return f"✅ ACCESS APPROVED: {user_role} can {action} on {resource}"
            else:
                return f"❌ ACCESS DENIED: {user_role} cannot {action} on {resource}"
        
        def log_permission_check(user_role: str, resource: str, result: str) -> str:
            """Log permission check result"""
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] PERMISSION CHECK - Role: {user_role}, Resource: {resource}, Result: {result}"
            
            return f"📝 LOGGED: Permission check for {user_role} accessing {resource}"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="check_rbac")(check_rbac)
        agent.register_for_llm(name="check_rbac", description="Check role-based access control permissions")(check_rbac)
        
        agent.register_for_execution(name="filter_documents")(filter_documents)
        agent.register_for_llm(name="filter_documents", description="Filter documents based on user role")(filter_documents)
        
        agent.register_for_execution(name="validate_access")(validate_access)
        agent.register_for_llm(name="validate_access", description="Validate user access to resources")(validate_access)
        
        agent.register_for_execution(name="log_permission_check")(log_permission_check)
        agent.register_for_llm(name="log_permission_check", description="Log permission check events")(log_permission_check)
        
        self.created_agents[AgentTypes.PERMISSION_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_document_agent(self, vector_store_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Document Retrieval Agent"""
        config = self.config_manager.get_config(AgentTypes.DOCUMENT_AGENT)
        
        # Mock document database
        document_database = {
            "financial_summary.md": {
                "content": "FinSolve Q4 2024 revenue reached $50M, 25% growth over Q3. Operating expenses increased 15% due to expanded team.",
                "department": "finance",
                "keywords": ["revenue", "growth", "expenses", "Q4", "financial"],
                "embedding": [0.1, 0.2, 0.3, 0.4, 0.5]  # Mock embedding
            },
            "marketing_report_q3_2024.md": {
                "content": "Digital marketing campaign 'Growth Focus' achieved 40% customer acquisition increase. CAC decreased from $150 to $120.",
                "department": "marketing",
                "keywords": ["campaign", "acquisition", "CAC", "digital", "growth"],
                "embedding": [0.2, 0.3, 0.4, 0.5, 0.6]
            },
            "employee_handbook.md": {
                "content": "Employee vacation policy: 15 days annually for < 2 years tenure, 20 days for 2-5 years, 25 days for 5+ years.",
                "department": "hr",
                "keywords": ["vacation", "policy", "employee", "tenure", "days"],
                "embedding": [0.3, 0.4, 0.5, 0.6, 0.7]
            },
            "engineering_master_doc.md": {
                "content": "FinSolve architecture uses microservices with Docker. API gateway handles requests with rate limiting and auth.",
                "department": "engineering", 
                "keywords": ["architecture", "microservices", "docker", "API", "gateway"],
                "embedding": [0.4, 0.5, 0.6, 0.7, 0.8]
            }
        }
        
        # Document search tools
        def vector_search(query: str, max_results: int = 5) -> str:
            """Perform semantic search on documents"""
            query_lower = query.lower()
            results = []
            
            for doc_name, doc_data in document_database.items():
                # Simple keyword-based relevance scoring
                score = 0
                content_lower = doc_data["content"].lower()
                
                # Check query terms in content
                query_terms = query_lower.split()
                for term in query_terms:
                    if term in content_lower:
                        score += 10
                    if term in doc_data["keywords"]:
                        score += 5
                
                if score > 0:
                    results.append({
                        "document": doc_name,
                        "score": score,
                        "department": doc_data["department"],
                        "preview": doc_data["content"][:100] + "..."
                    })
            
            # Sort by score and limit results
            results.sort(key=lambda x: x["score"], reverse=True)
            results = results[:max_results]
            
            if not results:
                return f"🔍 No documents found for query: '{query}'"
            
            result_text = f"🔍 Found {len(results)} documents for '{query}':\n"
            for i, result in enumerate(results, 1):
                result_text += f"{i}. {result['document']} (Score: {result['score']}, Dept: {result['department']})\n"
                result_text += f"   Preview: {result['preview']}\n"
            
            return result_text
        
        def rank_documents(document_names: List[str], query: str) -> str:
            """Rank documents by relevance to query"""
            if not document_names:
                return "📋 No documents to rank"
            
            ranked = []
            query_lower = query.lower()
            
            for doc_name in document_names:
                if doc_name in document_database:
                    doc_data = document_database[doc_name]
                    
                    # Calculate relevance score
                    score = 0
                    if query_lower in doc_data["content"].lower():
                        score += 10
                    
                    for keyword in doc_data["keywords"]:
                        if keyword.lower() in query_lower:
                            score += 5
                    
                    ranked.append((doc_name, score))
            
            # Sort by score
            ranked.sort(key=lambda x: x[1], reverse=True)
            
            result_text = f"📊 Ranked {len(ranked)} documents for '{query}':\n"
            for i, (doc_name, score) in enumerate(ranked, 1):
                result_text += f"{i}. {doc_name} (Relevance: {score})\n"
            
            return result_text
        
        def filter_by_access(document_names: List[str], user_role: str) -> str:
            """Filter documents based on user access permissions"""
            # Mock access control
            role_access = {
                "finance_team": ["financial_summary.md", "marketing_report_q3_2024.md"],
                "marketing_team": ["marketing_report_q3_2024.md"],
                "hr_team": ["employee_handbook.md"],
                "engineering_team": ["engineering_master_doc.md"],
                "c_level_executives": list(document_database.keys()),
                "general_employees": []
            }
            
            accessible = role_access.get(user_role, [])
            filtered = [doc for doc in document_names if doc in accessible]
            
            return f"🔒 Filtered documents for {user_role}: {len(filtered)}/{len(document_names)} accessible"
        
        def get_document_metadata(document_name: str) -> str:
            """Get metadata for specific document"""
            if document_name not in document_database:
                return f"❌ Document not found: {document_name}"
            
            doc_data = document_database[document_name]
            metadata = f"📄 Metadata for {document_name}:\n"
            metadata += f"   Department: {doc_data['department']}\n"
            metadata += f"   Keywords: {', '.join(doc_data['keywords'])}\n"
            metadata += f"   Content length: {len(doc_data['content'])} characters\n"
            
            return metadata
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="vector_search")(vector_search)
        agent.register_for_llm(name="vector_search", description="Perform semantic search on documents")(vector_search)
        
        agent.register_for_execution(name="rank_documents")(rank_documents)
        agent.register_for_llm(name="rank_documents", description="Rank documents by relevance")(rank_documents)
        
        agent.register_for_execution(name="filter_by_access")(filter_by_access)
        agent.register_for_llm(name="filter_by_access", description="Filter documents by user access")(filter_by_access)
        
        agent.register_for_execution(name="get_document_metadata")(get_document_metadata)
        agent.register_for_llm(name="get_document_metadata", description="Get document metadata")(get_document_metadata)
        
        self.created_agents[AgentTypes.DOCUMENT_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_context_agent(self, rag_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Context Assembly Agent"""
        config = self.config_manager.get_config(AgentTypes.CONTEXT_AGENT)
        
        # Context assembly tools
        def assemble_context(document_contents: List[str], query: str, max_length: int = 2000) -> str:
            """Assemble coherent context from multiple documents"""
            if not document_contents:
                return "❌ No documents provided for context assembly"
            
            # Start with query-relevant summary
            context_parts = [f"Context for query: '{query}'\n"]
            
            current_length = len(context_parts[0])
            doc_summaries = []
            
            for i, content in enumerate(document_contents):
                if current_length >= max_length:
                    break
                
                # Create document summary
                preview_length = min(300, max_length - current_length - 50)
                if preview_length > 50:
                    doc_summary = f"Document {i+1}: {content[:preview_length]}..."
                    doc_summaries.append(doc_summary)
                    current_length += len(doc_summary)
            
            assembled_context = context_parts[0] + "\n\n".join(doc_summaries)
            
            return f"📝 Assembled context ({len(assembled_context)} chars):\n{assembled_context}"
        
        def summarize_documents(document_contents: List[str], focus_keywords: List[str] = None) -> str:
            """Create focused summaries of multiple documents"""
            if not document_contents:
                return "❌ No documents to summarize"
            
            summaries = []
            focus_keywords = focus_keywords or []
            
            for i, content in enumerate(document_contents):
                # Extract key sentences (simple approach)
                sentences = content.split('. ')
                key_sentences = []
                
                for sentence in sentences[:3]:  # Take first 3 sentences
                    if focus_keywords:
                        if any(keyword.lower() in sentence.lower() for keyword in focus_keywords):
                            key_sentences.append(sentence)
                    else:
                        key_sentences.append(sentence)
                
                if key_sentences:
                    summary = '. '.join(key_sentences[:2]) + '.'
                    summaries.append(f"Doc {i+1}: {summary}")
            
            return f"📋 Document summaries:\n" + "\n".join(summaries)
        
        def optimize_context_length(context: str, target_length: int) -> str:
            """Optimize context to fit within target length"""
            if len(context) <= target_length:
                return f"✅ Context already optimal ({len(context)} chars)"
            
            # Simple truncation with ellipsis
            optimized = context[:target_length-3] + "..."
            
            return f"✂️ Optimized context to {len(optimized)} chars (from {len(context)})"
        
        def prioritize_sources(sources: List[Dict], user_role: str) -> str:
            """Prioritize sources based on user role and relevance"""
            if not sources:
                return "❌ No sources to prioritize"
            
            # Role-based prioritization
            role_priorities = {
                "finance_team": ["financial", "budget", "revenue", "cost"],
                "marketing_team": ["marketing", "campaign", "customer", "acquisition"],
                "hr_team": ["employee", "policy", "handbook", "procedure"],
                "engineering_team": ["technical", "architecture", "development", "system"],
                "c_level_executives": ["strategic", "performance", "growth", "overview"]
            }
            
            priorities = role_priorities.get(user_role, [])
            
            # Score sources based on role relevance
            scored_sources = []
            for source in sources:
                score = 0
                content = source.get("content", "").lower()
                
                for priority_term in priorities:
                    if priority_term in content:
                        score += 10
                
                scored_sources.append((source, score))
            
            # Sort by score
            scored_sources.sort(key=lambda x: x[1], reverse=True)
            
            result = f"📊 Prioritized {len(sources)} sources for {user_role}:\n"
            for i, (source, score) in enumerate(scored_sources[:5], 1):
                result += f"{i}. {source.get('name', f'Source {i}')} (Score: {score})\n"
            
            return result
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="assemble_context")(assemble_context)
        agent.register_for_llm(name="assemble_context", description="Assemble coherent context from documents")(assemble_context)
        
        agent.register_for_execution(name="summarize_documents")(summarize_documents)
        agent.register_for_llm(name="summarize_documents", description="Create focused document summaries")(summarize_documents)
        
        agent.register_for_execution(name="optimize_context_length")(optimize_context_length)
        agent.register_for_llm(name="optimize_context_length", description="Optimize context to target length")(optimize_context_length)
        
        agent.register_for_execution(name="prioritize_sources")(prioritize_sources)
        agent.register_for_llm(name="prioritize_sources", description="Prioritize sources by user role")(prioritize_sources)
        
        self.created_agents[AgentTypes.CONTEXT_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_generation_agent(self, generation_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Response Generation Agent"""
        config = self.config_manager.get_config(AgentTypes.GENERATION_AGENT)
        
        # Response generation tools
        def generate_response(query: str, context: str, user_role: str) -> str:
            """Generate response based on query, context, and user role"""
            if not query or not context:
                return "❌ Missing query or context for response generation"
            
            # Role-specific response formatting
            role_styles = {
                "finance_team": "Focus on financial metrics, costs, and ROI. Use specific numbers when available.",
                "marketing_team": "Emphasize customer impact, campaign performance, and market insights.",
                "hr_team": "Provide clear policy guidance and procedural information.",
                "engineering_team": "Include technical details and implementation considerations.",
                "c_level_executives": "Provide strategic overview with key business metrics and implications.",
                "general_employees": "Keep explanations clear and accessible, focusing on practical information."
            }
            
            style_guide = role_styles.get(user_role, "Provide clear, professional information.")
            
            # Mock response generation (would use actual LLM in production)
            response = f"""Based on the provided context, here's the information relevant to your query "{query}":

{style_guide}

Context Summary: {context[:200]}...

[This would be a comprehensive response generated by the LLM based on the context and user role]

Key Points:
- Information tailored for {user_role} perspective
- Based on provided company documents
- Includes relevant sources and citations
"""
            
            return f"💬 Generated response ({len(response)} chars):\n{response}"
        
        def cite_sources(response_text: str, source_documents: List[str]) -> str:
            """Add proper source citations to response"""
            if not source_documents:
                return response_text + "\n\n📚 No sources provided for citation."
            
            citations = "\n\n📚 Sources:\n"
            for i, source in enumerate(source_documents, 1):
                citations += f"{i}. {source}\n"
            
            return response_text + citations
        
        def adapt_tone(response_text: str, target_role: str) -> str:
            """Adapt response tone for target user role"""
            tone_adaptations = {
                "c_level_executives": "Executive Summary style - strategic and high-level",
                "finance_team": "Financial analysis style - data-driven and precise",
                "marketing_team": "Marketing insights style - customer-focused and actionable",
                "hr_team": "Policy guidance style - clear and procedural",
                "engineering_team": "Technical documentation style - detailed and systematic",
                "general_employees": "General communication style - accessible and clear"
            }
            
            adaptation = tone_adaptations.get(target_role, "Professional business style")
            
            return f"🎯 Tone adapted for {target_role}: {adaptation}\n\n{response_text}"
        
        def validate_accuracy(response_text: str, source_context: str) -> str:
            """Validate response accuracy against source context"""
            # Simple accuracy check (would be more sophisticated in production)
            response_lower = response_text.lower()
            context_lower = source_context.lower()
            
            # Check for potential hallucinations (information not in context)
            accuracy_score = 85  # Mock score
            
            if accuracy_score >= 90:
                return f"✅ HIGH ACCURACY: Response well-supported by sources ({accuracy_score}%)"
            elif accuracy_score >= 75:
                return f"⚠️  MEDIUM ACCURACY: Some claims may need verification ({accuracy_score}%)"
            else:
                return f"❌ LOW ACCURACY: Response may contain unsupported claims ({accuracy_score}%)"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="generate_response")(generate_response)
        agent.register_for_llm(name="generate_response", description="Generate response based on context and user role")(generate_response)
        
        agent.register_for_execution(name="cite_sources")(cite_sources)
        agent.register_for_llm(name="cite_sources", description="Add source citations to response")(cite_sources)
        
        agent.register_for_execution(name="adapt_tone")(adapt_tone)
        agent.register_for_llm(name="adapt_tone", description="Adapt response tone for user role")(adapt_tone)
        
        agent.register_for_execution(name="validate_accuracy")(validate_accuracy)
        agent.register_for_llm(name="validate_accuracy", description="Validate response accuracy")(validate_accuracy)
        
        self.created_agents[AgentTypes.GENERATION_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_quality_agent(self, quality_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Quality Assurance Agent"""
        config = self.config_manager.get_config(AgentTypes.QUALITY_AGENT)
        
        # Quality assurance tools
        def validate_accuracy(response: str, source_context: str) -> str:
            """Validate response accuracy against sources"""
            if not response or not source_context:
                return "❌ Missing response or context for validation"
            
            # Mock accuracy validation
            issues = []
            
            # Check for common quality issues
            if len(response) < 50:
                issues.append("Response too brief")
            
            if "http://" in response or "https://" in response:
                issues.append("Contains external links (potential security risk)")
            
            if not any(char.isupper() for char in response):
                issues.append("Missing proper capitalization")
            
            accuracy_score = max(0, 100 - len(issues) * 20)
            
            if not issues:
                return f"✅ QUALITY CHECK PASSED: No issues found (Score: {accuracy_score}%)"
            else:
                return f"⚠️  QUALITY ISSUES: {', '.join(issues)} (Score: {accuracy_score}%)"
        
        def check_citations(response: str) -> str:
            """Check if response has proper source citations"""
            citation_indicators = ["sources:", "source:", "reference:", "from:", "according to"]
            
            response_lower = response.lower()
            has_citations = any(indicator in response_lower for indicator in citation_indicators)
            
            if has_citations:
                return "✅ CITATIONS: Response includes source references"
            else:
                return "⚠️  MISSING CITATIONS: Response should include source references"
        
        def assess_tone(response: str, target_role: str) -> str:
            """Assess if response tone is appropriate for target role"""
            # Mock tone assessment
            tone_scores = {
                "professionalism": 85,
                "clarity": 90,
                "role_appropriateness": 80
            }
            
            overall_score = sum(tone_scores.values()) / len(tone_scores)
            
            if overall_score >= 85:
                return f"✅ TONE APPROPRIATE: Good professional tone (Score: {overall_score:.1f}%)"
            else:
                return f"⚠️  TONE ISSUES: May need adjustment for {target_role} (Score: {overall_score:.1f}%)"
        
        def score_quality(response: str, criteria: Dict = None) -> str:
            """Score overall response quality"""
            criteria = criteria or {
                "accuracy": 0.3,
                "completeness": 0.25,
                "clarity": 0.25,
                "professionalism": 0.2
            }
            
            # Mock quality scoring
            scores = {
                "accuracy": 85,
                "completeness": 80,
                "clarity": 90,
                "professionalism": 88
            }
            
            weighted_score = sum(scores[metric] * weight for metric, weight in criteria.items())
            
            quality_grade = "A" if weighted_score >= 90 else "B" if weighted_score >= 80 else "C" if weighted_score >= 70 else "D"
            
            return f"📊 QUALITY SCORE: {weighted_score:.1f}% (Grade: {quality_grade})"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="validate_accuracy")(validate_accuracy)
        agent.register_for_llm(name="validate_accuracy", description="Validate response accuracy")(validate_accuracy)
        
        agent.register_for_execution(name="check_citations")(check_citations)
        agent.register_for_llm(name="check_citations", description="Check source citations")(check_citations)
        
        agent.register_for_execution(name="assess_tone")(assess_tone)
        agent.register_for_llm(name="assess_tone", description="Assess response tone appropriateness")(assess_tone)
        
        agent.register_for_execution(name="score_quality")(score_quality)
        agent.register_for_llm(name="score_quality", description="Score overall response quality")(score_quality)
        
        self.created_agents[AgentTypes.QUALITY_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_analytics_agent(self, analytics_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Analytics Agent"""
        config = self.config_manager.get_config(AgentTypes.ANALYTICS_AGENT)
        
        # Mock analytics data store
        analytics_data = {
            "query_counts": {"finance_team": 45, "marketing_team": 32, "hr_team": 18},
            "response_times": {"avg": 2.3, "min": 0.8, "max": 5.2},
            "satisfaction_scores": {"finance_team": 4.2, "marketing_team": 4.5, "hr_team": 4.1},
            "popular_documents": ["financial_summary.md", "marketing_report_q3_2024.md"],
            "error_rates": {"auth_errors": 2, "permission_errors": 1, "system_errors": 0}
        }
        
        # Analytics tools
        def track_metrics(metric_type: str, value: float, metadata: Dict = None) -> str:
            """Track system performance metrics"""
            timestamp = datetime.now().isoformat()
            
            # Mock metric tracking
            metrics_logged = {
                "timestamp": timestamp,
                "metric_type": metric_type,
                "value": value,
                "metadata": metadata or {}
            }
            
            return f"📊 TRACKED: {metric_type} = {value} at {timestamp[:19]}"
        
        def analyze_patterns(data_type: str, time_period: str = "24h") -> str:
            """Analyze usage patterns and trends"""
            if data_type == "queries":
                pattern_analysis = f"Query Pattern Analysis ({time_period}):\n"
                pattern_analysis += "• Peak usage: 2-4 PM (business hours)\n"
                pattern_analysis += "• Finance team: 40% of queries\n"
                pattern_analysis += "• Most common queries: financial reports, budgets\n"
                pattern_analysis += "• Average session duration: 8.5 minutes"
                
            elif data_type == "performance":
                pattern_analysis = f"Performance Pattern Analysis ({time_period}):\n"
                pattern_analysis += f"• Average response time: {analytics_data['response_times']['avg']}s\n"
                pattern_analysis += "• 95% of queries < 5s response time\n"
                pattern_analysis += "• Zero system downtime\n"
                pattern_analysis += "• Memory usage stable at 65%"
                
            elif data_type == "errors":
                pattern_analysis = f"Error Pattern Analysis ({time_period}):\n"
                pattern_analysis += f"• Authentication errors: {analytics_data['error_rates']['auth_errors']}\n"
                pattern_analysis += f"• Permission errors: {analytics_data['error_rates']['permission_errors']}\n"
                pattern_analysis += "• Error rate: 0.3% (excellent)\n"
                pattern_analysis += "• Most errors resolved automatically"
                
            else:
                pattern_analysis = f"Unknown data type: {data_type}"
            
            return f"🔍 {pattern_analysis}"
        
        def generate_reports(report_type: str, format: str = "summary") -> str:
            """Generate analytics reports"""
            if report_type == "usage":
                report = "📈 USAGE REPORT\n"
                report += "===============\n"
                report += f"Total Queries: {sum(analytics_data['query_counts'].values())}\n"
                report += "Query Distribution:\n"
                for role, count in analytics_data['query_counts'].items():
                    report += f"  • {role}: {count} queries\n"
                report += f"\nAverage Satisfaction: {np.mean(list(analytics_data['satisfaction_scores'].values())):.1f}/5.0"
                
            elif report_type == "performance":
                report = "⚡ PERFORMANCE REPORT\n"
                report += "===================\n"
                report += f"Average Response Time: {analytics_data['response_times']['avg']}s\n"
                report += f"Fastest Response: {analytics_data['response_times']['min']}s\n"
                report += f"Slowest Response: {analytics_data['response_times']['max']}s\n"
                report += "System Health: ✅ Excellent\n"
                report += "Uptime: 99.9%"
                
            elif report_type == "security":
                report = "🔒 SECURITY REPORT\n"
                report += "==================\n"
                total_errors = sum(analytics_data['error_rates'].values())
                report += f"Total Security Events: {total_errors}\n"
                report += "Security Status: ✅ Secure\n"
                report += "RBAC Compliance: 100%\n"
                report += "No unauthorized access attempts"
                
            else:
                report = f"❌ Unknown report type: {report_type}"
            
            return report
        
        def monitor_performance(component: str) -> str:
            """Monitor real-time performance of system components"""
            # Mock performance monitoring
            performance_data = {
                "auth_agent": {"status": "healthy", "response_time": 0.8, "memory": "12MB"},
                "document_agent": {"status": "healthy", "response_time": 1.2, "memory": "45MB"},
                "generation_agent": {"status": "healthy", "response_time": 2.1, "memory": "78MB"},
                "vector_store": {"status": "healthy", "query_time": 0.3, "size": "2.1GB"}
            }
            
            if component in performance_data:
                data = performance_data[component]
                status = data["status"]
                icon = "✅" if status == "healthy" else "⚠️" if status == "warning" else "❌"
                
                monitor_result = f"{icon} {component.upper()} STATUS\n"
                monitor_result += f"Status: {status}\n"
                
                if "response_time" in data:
                    monitor_result += f"Response Time: {data['response_time']}s\n"
                if "memory" in data:
                    monitor_result += f"Memory Usage: {data['memory']}\n"
                if "query_time" in data:
                    monitor_result += f"Query Time: {data['query_time']}s\n"
                if "size" in data:
                    monitor_result += f"Storage Size: {data['size']}\n"
                
                return monitor_result
            else:
                return f"❌ Unknown component: {component}"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config=config.to_llm_config(),
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="track_metrics")(track_metrics)
        agent.register_for_llm(name="track_metrics", description="Track system performance metrics")(track_metrics)
        
        agent.register_for_execution(name="analyze_patterns")(analyze_patterns)
        agent.register_for_llm(name="analyze_patterns", description="Analyze usage patterns and trends")(analyze_patterns)
        
        agent.register_for_execution(name="generate_reports")(generate_reports)
        agent.register_for_llm(name="generate_reports", description="Generate analytics reports")(generate_reports)
        
        agent.register_for_execution(name="monitor_performance")(monitor_performance)
        agent.register_for_llm(name="monitor_performance", description="Monitor component performance")(monitor_performance)
        
        self.created_agents[AgentTypes.ANALYTICS_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_audit_agent(self, audit_context: Optional[Dict] = None) -> ConversableAgent:
        """Create Audit Agent"""
        config = self.config_manager.get_config(AgentTypes.AUDIT_AGENT)
        
        # Mock audit log storage
        audit_logs = []
        
        # Audit tools
        def log_event(event_type: str, user_id: str, resource: str, action: str, result: str) -> str:
            """Log system events for audit trail"""
            timestamp = datetime.now().isoformat()
            
            log_entry = {
                "timestamp": timestamp,
                "event_type": event_type,
                "user_id": user_id,
                "resource": resource,
                "action": action,
                "result": result,
                "session_id": f"sess_{hash(user_id + timestamp) % 10000:04d}"
            }
            
            audit_logs.append(log_entry)
            
            return f"📝 AUDIT LOG: {event_type} - {user_id} {action} {resource} -> {result}"
        
        def generate_audit_report(time_period: str = "24h", event_type: str = "all") -> str:
            """Generate comprehensive audit report"""
            if not audit_logs:
                return "📋 No audit events recorded"
            
            report = f"🔍 AUDIT REPORT ({time_period})\n"
            report += "=" * 30 + "\n"
            
            # Filter logs by event type if specified
            filtered_logs = audit_logs
            if event_type != "all":
                filtered_logs = [log for log in audit_logs if log["event_type"] == event_type]
            
            # Summary statistics
            total_events = len(filtered_logs)
            unique_users = len(set(log["user_id"] for log in filtered_logs))
            success_count = len([log for log in filtered_logs if "success" in log["result"].lower()])
            
            report += f"Total Events: {total_events}\n"
            report += f"Unique Users: {unique_users}\n"
            report += f"Success Rate: {(success_count/total_events*100 if total_events > 0 else 0):.1f}%\n\n"
            
            # Recent events
            report += "Recent Events:\n"
            for log in filtered_logs[-5:]:  # Last 5 events
                report += f"  {log['timestamp'][:19]} - {log['user_id']} {log['action']} {log['resource']}\n"
            
            return report
        
        def check_compliance(regulation: str) -> str:
            """Check compliance with specific regulations"""
            compliance_checks = {
                "data_protection": {
                    "required": ["user_consent_logged", "data_access_tracked", "retention_policy_applied"],
                    "status": "compliant"
                },
                "financial_reporting": {
                    "required": ["access_controls", "audit_trails", "data_integrity"],
                    "status": "compliant"
                },
                "security_standards": {
                    "required": ["authentication_logged", "authorization_tracked", "security_events_monitored"],
                    "status": "compliant"
                }
            }
            
            if regulation in compliance_checks:
                check = compliance_checks[regulation]
                compliance_report = f"🛡️  COMPLIANCE CHECK: {regulation.upper()}\n"
                compliance_report += f"Status: {check['status'].upper()}\n"
                compliance_report += "Required Controls:\n"
                for control in check["required"]:
                    compliance_report += f"  ✅ {control.replace('_', ' ').title()}\n"
                
                return compliance_report
            else:
                return f"❌ Unknown regulation: {regulation}"
        
        def track_changes(component: str, change_type: str, details: str) -> str:
            """Track system changes for audit purposes"""
            timestamp = datetime.now().isoformat()
            
            change_log = {
                "timestamp": timestamp,
                "component": component,
                "change_type": change_type,
                "details": details,
                "logged_by": "system"
            }
            
            # Mock change tracking (would persist to actual audit database)
            return f"📋 CHANGE TRACKED: {change_type} to {component} - {details}"
        
        # Create the agent
        agent = ConversableAgent(
            name=config.name,
            system_message=config.system_message,
            llm_config={"model": "gpt-4-mini", "temperature": 0.0},  # Override for audit agent
            max_consecutive_auto_reply=config.max_consecutive_auto_reply,
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="log_event")(log_event)
        agent.register_for_llm(name="log_event", description="Log system events for audit trail")(log_event)
        
        agent.register_for_execution(name="generate_audit_report")(generate_audit_report)
        agent.register_for_llm(name="generate_audit_report", description="Generate audit reports")(generate_audit_report)
        
        agent.register_for_execution(name="check_compliance")(check_compliance)
        agent.register_for_llm(name="check_compliance", description="Check regulatory compliance")(check_compliance)
        
        agent.register_for_execution(name="track_changes")(track_changes)
        agent.register_for_llm(name="track_changes", description="Track system changes")(track_changes)
        
        self.created_agents[AgentTypes.AUDIT_AGENT] = agent
        print(f"✅ Created {config.name} with {len(config.tools)} tools")
        
        return agent
    
    def create_all_agents(self) -> Dict[AgentTypes, ConversableAgent]:
        """Create all individual agents"""
        print("\n🏭 Creating all individual agents...")
        
        agents = {}
        
        # Create each agent type
        agents[AgentTypes.AUTH_AGENT] = self.create_auth_agent()
        agents[AgentTypes.PERMISSION_AGENT] = self.create_permission_agent()
        agents[AgentTypes.DOCUMENT_AGENT] = self.create_document_agent()
        agents[AgentTypes.CONTEXT_AGENT] = self.create_context_agent()
        agents[AgentTypes.GENERATION_AGENT] = self.create_generation_agent()
        agents[AgentTypes.QUALITY_AGENT] = self.create_quality_agent()
        agents[AgentTypes.ANALYTICS_AGENT] = self.create_analytics_agent()
        agents[AgentTypes.AUDIT_AGENT] = self.create_audit_agent()
        
        print(f"\n✅ Successfully created {len(agents)} individual agents")
        
        return agents

# ===============================
# 3. INDIVIDUAL AGENT TESTING
# ===============================

class IndividualAgentTester:
    """Comprehensive testing framework for individual agents"""
    
    def __init__(self, agents: Dict[AgentTypes, ConversableAgent]):
        self.agents = agents
        self.test_results = {}
        
        print("🧪 Individual Agent Tester initialized")
        print(f"🤖 Testing {len(agents)} agents")
    
    async def test_auth_agent(self) -> Dict:
        """Test Authentication Agent functionality"""
        print("\n🔐 Testing Authentication Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.AUTH_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        # Test cases for auth agent
        test_cases = [
            {
                "name": "Valid session validation",
                "message": "Please validate session sess_12345abc",
                "expected_keywords": ["valid", "active"]
            },
            {
                "name": "Invalid session validation", 
                "message": "Please validate session invalid_123",
                "expected_keywords": ["invalid", "not found"]
            },
            {
                "name": "Token generation",
                "message": "Generate a token for user finance_001 with role finance_team",
                "expected_keywords": ["token generated", "expires"]
            },
            {
                "name": "Credential check",
                "message": "Check credentials for admin with hash admin_hash_123",
                "expected_keywords": ["authenticated", "valid"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                
                # Check if expected keywords are present
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL - Expected keywords not found")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": "ERROR",
                    "error": str(e)
                })
        
        return test_results
    
    async def test_permission_agent(self) -> Dict:
        """Test Permission Agent functionality"""
        print("\n🛡️  Testing Permission Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.PERMISSION_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Finance team access check",
                "message": "Check if finance_team can access financial_reports",
                "expected_keywords": ["granted", "can access"]
            },
            {
                "name": "General employee restriction",
                "message": "Check if general_employees can access financial_reports",
                "expected_keywords": ["denied", "cannot access"]
            },
            {
                "name": "Document filtering",
                "message": "Filter documents for marketing_team: financial_summary.md, marketing_report.md",
                "expected_keywords": ["filtered", "accessible"]
            },
            {
                "name": "C-level full access",
                "message": "Validate c_level_executives access to all_documents",
                "expected_keywords": ["approved", "access"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_document_agent(self) -> Dict:
        """Test Document Agent functionality"""
        print("\n📄 Testing Document Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.DOCUMENT_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Financial document search",
                "message": "Search for documents about revenue and financial performance",
                "expected_keywords": ["found", "financial_summary", "revenue"]
            },
            {
                "name": "Marketing document search",
                "message": "Find documents about campaigns and customer acquisition",
                "expected_keywords": ["found", "marketing", "campaign"]
            },
            {
                "name": "Document ranking",
                "message": "Rank these documents by relevance to budget: financial_summary.md, marketing_report.md",
                "expected_keywords": ["ranked", "relevance"]
            },
            {
                "name": "Document metadata",
                "message": "Get metadata for financial_summary.md",
                "expected_keywords": ["metadata", "department", "keywords"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_context_agent(self) -> Dict:
        """Test Context Agent functionality"""
        print("\n🧠 Testing Context Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.CONTEXT_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Context assembly",
                "message": "Assemble context from these documents for query 'financial performance': ['Q4 revenue was $50M', 'Operating costs increased 15%']",
                "expected_keywords": ["assembled", "context", "financial performance"]
            },
            {
                "name": "Document summarization", 
                "message": "Summarize documents focusing on keywords: revenue, growth",
                "expected_keywords": ["summaries", "revenue", "growth"]
            },
            {
                "name": "Context optimization",
                "message": "Optimize this context to 500 characters: 'This is a very long context that needs to be shortened...'",
                "expected_keywords": ["optimized", "chars"]
            },
            {
                "name": "Source prioritization",
                "message": "Prioritize sources for finance_team from: [{'name': 'budget.pdf', 'content': 'budget analysis'}, {'name': 'marketing.pdf', 'content': 'marketing data'}]",
                "expected_keywords": ["prioritized", "finance_team"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_generation_agent(self) -> Dict:
        """Test Generation Agent functionality"""
        print("\n💬 Testing Generation Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.GENERATION_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Response generation",
                "message": "Generate response for query 'What is our Q4 revenue?' with context 'Q4 revenue was $50M, 25% growth' for finance_team",
                "expected_keywords": ["response", "50m", "finance"]
            },
            {
                "name": "Source citation",
                "message": "Add citations to this response: 'Revenue increased significantly' using sources: ['financial_report.pdf', 'budget_analysis.xlsx']",
                "expected_keywords": ["sources", "financial_report", "budget_analysis"]
            },
            {
                "name": "Tone adaptation",
                "message": "Adapt this response for c_level_executives: 'The numbers look good'",
                "expected_keywords": ["adapted", "c_level_executives", "executive"]
            },
            {
                "name": "Accuracy validation",
                "message": "Validate accuracy of 'Revenue grew 25%' against context 'Q4 revenue was $50M, 25% growth over Q3'",
                "expected_keywords": ["accuracy", "supported"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_quality_agent(self) -> Dict:
        """Test Quality Agent functionality"""
        print("\n✅ Testing Quality Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.QUALITY_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Accuracy validation",
                "message": "Validate accuracy of 'Revenue is $50M' against source 'Q4 revenue reached $50M with 25% growth'",
                "expected_keywords": ["quality", "passed", "score"]
            },
            {
                "name": "Citation check",
                "message": "Check citations in: 'Revenue grew 25% according to financial reports. Source: budget_analysis.pdf'",
                "expected_keywords": ["citations", "references"]
            },
            {
                "name": "Tone assessment",
                "message": "Assess tone appropriateness for c_level_executives: 'Hey, the numbers are pretty good!'",
                "expected_keywords": ["tone", "appropriate", "professional"]
            },
            {
                "name": "Quality scoring",
                "message": "Score quality of this response: 'Revenue increased to $50M in Q4, representing 25% growth. Source: Financial Report 2024'",
                "expected_keywords": ["quality score", "grade"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_analytics_agent(self) -> Dict:
        """Test Analytics Agent functionality"""
        print("\n📊 Testing Analytics Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.ANALYTICS_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Metric tracking",
                "message": "Track metric response_time with value 2.3 seconds",
                "expected_keywords": ["tracked", "response_time", "2.3"]
            },
            {
                "name": "Pattern analysis",
                "message": "Analyze query patterns for the last 24 hours",
                "expected_keywords": ["pattern analysis", "24h", "queries"]
            },
            {
                "name": "Usage report generation",
                "message": "Generate a usage report in summary format",
                "expected_keywords": ["usage report", "queries", "distribution"]
            },
            {
                "name": "Performance monitoring",
                "message": "Monitor performance of document_agent component",
                "expected_keywords": ["status", "document_agent", "healthy"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def test_audit_agent(self) -> Dict:
        """Test Audit Agent functionality"""
        print("\n📝 Testing Audit Agent...")
        print("-" * 40)
        
        agent = self.agents[AgentTypes.AUDIT_AGENT]
        test_results = {"passed": 0, "failed": 0, "tests": []}
        
        test_cases = [
            {
                "name": "Event logging",
                "message": "Log event: authentication user_123 accessed financial_reports with result success",
                "expected_keywords": ["audit log", "authentication", "user_123"]
            },
            {
                "name": "Audit report generation",
                "message": "Generate audit report for the last 24 hours, all event types",
                "expected_keywords": ["audit report", "24h", "events"]
            },
            {
                "name": "Compliance check",
                "message": "Check compliance with data_protection regulations",
                "expected_keywords": ["compliance check", "data_protection", "compliant"]
            },
            {
                "name": "Change tracking",
                "message": "Track change: permission_matrix updated with details 'Added finance team access to marketing reports'",
                "expected_keywords": ["change tracked", "permission_matrix", "updated"]
            }
        ]
        
        for test_case in test_cases:
            try:
                print(f"  🧪 {test_case['name']}...")
                
                response = agent.generate_reply(
                    messages=[{"role": "user", "content": test_case["message"]}]
                )
                
                response_str = str(response).lower()
                keywords_found = any(keyword in response_str for keyword in test_case["expected_keywords"])
                
                if keywords_found:
                    print(f"    ✅ PASS")
                    test_results["passed"] += 1
                    test_result = "PASS"
                else:
                    print(f"    ❌ FAIL")
                    test_results["failed"] += 1
                    test_result = "FAIL"
                
                test_results["tests"].append({
                    "name": test_case["name"],
                    "result": test_result,
                    "response_length": len(str(response))
                })
                
            except Exception as e:
                print(f"    ❌ ERROR: {str(e)}")
                test_results["failed"] += 1
        
        return test_results
    
    async def run_comprehensive_tests(self) -> Dict:
        """Run comprehensive tests on all individual agents"""
        print("\n🧪 Running Comprehensive Individual Agent Tests...")
        print("=" * 70)
        
        all_results = {}
        
        # Test each agent
        all_results["auth_agent"] = await self.test_auth_agent()
        all_results["permission_agent"] = await self.test_permission_agent()
        all_results["document_agent"] = await self.test_document_agent()
        all_results["context_agent"] = await self.test_context_agent()
        all_results["generation_agent"] = await self.test_generation_agent()
        all_results["quality_agent"] = await self.test_quality_agent()
        all_results["analytics_agent"] = await self.test_analytics_agent()
        all_results["audit_agent"] = await self.test_audit_agent()
        
        # Calculate overall statistics
        total_tests = sum(result["passed"] + result["failed"] for result in all_results.values())
        total_passed = sum(result["passed"] for result in all_results.values())
        total_failed = sum(result["failed"] for result in all_results.values())
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "=" * 70)
        print("📊 INDIVIDUAL AGENT TESTING SUMMARY")
        print("=" * 70)
        print(f"📋 Total Tests: {total_tests}")
        print(f"✅ Passed: {total_passed}")
        print(f"❌ Failed: {total_failed}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        print(f"\n📊 Agent Breakdown:")
        for agent_name, results in all_results.items():
            agent_success = (results["passed"] / (results["passed"] + results["failed"]) * 100) if (results["passed"] + results["failed"]) > 0 else 0
            print(f"   🤖 {agent_name}: {results['passed']}/{results['passed'] + results['failed']} ({agent_success:.1f}%)")
        
        if success_rate >= 90:
            print("\n🎉 Excellent! All agents are working properly!")
        elif success_rate >= 75:
            print("\n👍 Good! Most agents are working with minor issues")
        else:
            print("\n⚠️  Some agents need attention - check failed tests")
        
        return {
            "individual_results": all_results,
            "summary": {
                "total_tests": total_tests,
                "total_passed": total_passed,
                "total_failed": total_failed,
                "success_rate": success_rate
            }
        }

# ===============================
# 4. MAIN EXECUTION & DEMONSTRATION
# ===============================

async def main():
    """Main execution function for individual agent creation and testing"""
    print("🚀 Starting Individual Agent Creation & Testing Pipeline...")
    
    # Initialize ModelClient if available
    model_client = None
    if ModelClient:
        try:
            model_client = ModelClient()
            print("✅ ModelClient initialized for agents")
        except Exception as e:
            print(f"⚠️  ModelClient not available: {str(e)}")
    
    # Initialize agent factory
    print("\n🏭 Initializing Agent Factory...")
    agent_factory = IndividualAgentFactory(model_client)
    
    # Create all individual agents
    print("\n🤖 Creating All Individual Agents...")
    agents = agent_factory.create_all_agents()
    
    # Initialize and run comprehensive tests
    print("\n🧪 Initializing Test Suite...")
    tester = IndividualAgentTester(agents)
    
    # Run all tests
    test_results = await tester.run_comprehensive_tests()
    
    print("\n✅ Individual Agent Creation & Testing completed!")
    print("🎯 Ready for next phase: Agent-to-Agent Communication (Notebook 06)")
    
    return agents, test_results

# Run the main execution
if __name__ == "__main__":
    individual_agents, comprehensive_test_results = asyncio.run(main())

# ===============================
# 5. INTERACTIVE AGENT DEMONSTRATION
# ===============================

print("\n" + "=" * 70)
print("🧪 INTERACTIVE AGENT DEMONSTRATION")
print("=" * 70)

# Interactive functions for testing individual agents
if 'individual_agents' in locals() and individual_agents:
    
    def demonstrate_agent(agent_type: AgentTypes, test_message: str):
        """Demonstrate a specific agent with a test message"""
        if agent_type not in individual_agents:
            print(f"❌ Agent {agent_type.value} not available")
            return
        
        agent = individual_agents[agent_type]
        print(f"\n🤖 Testing {agent_type.value.upper()}:")
        print(f"📝 Message: {test_message}")
        
        try:
            response = agent.generate_reply(
                messages=[{"role": "user", "content": test_message}]
            )
            print(f"💬 Response: {response}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    def list_agent_capabilities():
        """List all agents and their capabilities"""
        print("\n🤖 Available Individual Agents:")
        print("-" * 40)
        
        for agent_type, agent in individual_agents.items():
            config = AgentConfigManager().get_config(agent_type)
            print(f"🔧 {agent_type.value.upper()}:")
            print(f"   Description: {config.description}")
            print(f"   Tools: {', '.join(config.tools)}")
            print(f"   Model: {config.model}")
            print("")
    
    def test_agent_workflow(user_query: str, user_role: str = "finance_team"):
        """Test a complete agent workflow with user query"""
        print(f"\n🔄 Testing Agent Workflow:")
        print(f"👤 User Role: {user_role}")
        print(f"💭 Query: {user_query}")
        print("-" * 50)
        
        # Step 1: Authentication
        print("1. 🔐 Authentication Check...")
        demonstrate_agent(AgentTypes.AUTH_AGENT, f"Validate session for {user_role}")
        
        # Step 2: Permission Check
        print("\n2. 🛡️  Permission Check...")