# Install required packages if not already installed
!pip install pyautogen>=0.4.0 autogen-agentchat>=0.4.0 autogen-core>=0.4.0
!pip install euriai python-dotenv
!pip install jupyter ipywidgets  # For interactive widgets

import sys
import os
import asyncio
import json
import time
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src directory to path to import our model_client
notebook_dir = os.path.dirname(os.path.abspath(''))
if 'notebooks' in notebook_dir:
    project_root = os.path.dirname(notebook_dir)
else:
    project_root = notebook_dir

src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

print(f"📂 Project root: {project_root}")
print(f"📂 Looking for model_client.py in: {src_path}")
print(f"✅ Python path updated")

# Test importing our custom model client
try:
    from model_client import (
        QuietEuriChatCompletionClient,
        AVAILABLE_MODELS,
        DSA_OPTIMIZED_MODELS,
        create_quiet_euri_client,
        get_dsa_optimized_model,
        create_dsa_client
    )
    print("✅ Successfully imported model_client.py")
    print(f"📊 Available models: {len(AVAILABLE_MODELS)}")
    
except ImportError as e:
    print(f"❌ Failed to import model_client.py: {e}")
    print("🔧 Make sure model_client.py is in the src/ directory")

# Import AutoGen 0.4 components
try:
    from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
    from autogen_agentchat.messages import TextMessage
    from autogen_core.models import UserMessage, AssistantMessage, SystemMessage
    print("✅ AutoGen 0.4 imports successful")
    print("📦 Available agents: AssistantAgent, UserProxyAgent")
    print("📦 Available message types: UserMessage, AssistantMessage, SystemMessage")
except ImportError as e:
    print(f"❌ AutoGen import failed: {e}")
    print("🔧 Make sure AutoGen 0.4+ is installed")

# Test creating AutoGen 0.4 agents with our model clients
print("🤖 Testing AutoGen 0.4 Agent Creation")
print("=" * 50)

try:
    # Create a simple model client
    client = create_quiet_euri_client("gpt-4.1-nano")
    print(f"✅ Created model client: {client.model_name}")
    
    # Create an AssistantAgent using AutoGen 0.4 syntax
    assistant = AssistantAgent(
        name="FinSolveAssistant",
        model_client=client,
        system_message="You are a helpful AI assistant for FinSolve Technologies. Provide clear, concise responses about financial services and technology."
    )
    
    print(f"✅ Created AssistantAgent: {assistant.name}")
    print(f"   Model Client: {type(client).__name__}")
    print(f"   System Message: {assistant.system_message[:50]}...")
    
    # Test basic functionality
    print("\n🧪 Testing basic agent functionality...")
    print("✅ Agent creation successful!")
    
except Exception as e:
    print(f"❌ Failed to create AutoGen agent: {e}")
    print("🔧 Make sure you're using AutoGen 0.4+ syntax")
    import traceback
    traceback.print_exc()

# Test creating agents with different models
test_models = [
    "gpt-4.1-nano",  # Fast, efficient
    "gpt-4.1-mini",  # Balanced
    "openai/gpt-4o", # Advanced
]

agents = {}

for model in test_models:
    try:
        # Create model client
        client = create_quiet_euri_client(model)
        
        # Create AssistantAgent with our model client
        agent = AssistantAgent(
            name=f"Agent_{model.replace('/', '_').replace('-', '_')}",
            model_client=client,
            system_message=f"You are an AI assistant using {client.model_name}. Provide helpful responses."
        )
        
        agents[model] = agent
        print(f"✅ Created agent with {client.model_name}")
        print(f"   Agent Name: {agent.name}")
        
    except Exception as e:
        print(f"❌ Failed to create agent with {model}: {e}")

print(f"\n📊 Successfully created {len(agents)} agents")

# Test basic conversation with an agent
if agents:
    # Get the first agent
    agent_name, agent = next(iter(agents.items()))
    print(f"🗣️ Testing conversation with {agent.name}")
    print("=" * 50)
    
    try:
        # Create a simple message
        test_message = TextMessage(
            content="Hello! Can you tell me about FinSolve Technologies?",
            source="user"
        )
        
        print(f"👤 User: {test_message.content}")
        print("🤖 Agent: [Response would appear here in a full conversation]")
        print("✅ Message creation successful!")
        
    except Exception as e:
        print(f"❌ Conversation test failed: {e}")
        import traceback
        traceback.print_exc()
else:
    print("❌ No agents available for testing")

# Summary of test results
print("📋 Test Summary")
print("=" * 30)
print(f"✅ Model client import: {'Success' if 'AVAILABLE_MODELS' in globals() else 'Failed'}")
print(f"✅ AutoGen 0.4 import: {'Success' if 'AssistantAgent' in globals() else 'Failed'}")
print(f"✅ Agent creation: {'Success' if 'agents' in globals() and agents else 'Failed'}")
print(f"📊 Total agents created: {len(agents) if 'agents' in globals() else 0}")
print(f"📊 Available models: {len(AVAILABLE_MODELS) if 'AVAILABLE_MODELS' in globals() else 0}")

if 'agents' in globals() and agents:
    print("\n🤖 Created Agents:")
    for model, agent in agents.items():
        print(f"   • {agent.name} ({model})")

print("\n🎯 Next Steps:")
print("1. Test actual conversations between agents")
print("2. Test streaming responses")
print("3. Test function calling capabilities")
print("4. Test DSA-optimized model selection")
print("5. Performance and cost analysis")