# Copyright (c) 2023 - 2025, AG2ai, Inc., AG2ai open-source projects maintainers and core contributors
#
# SPDX-License-Identifier: Apache-2.0
#
# Portions derived from  https://github.com/https://github.com/Lancetnik/FastDepends are under the MIT License.
# SPDX-License-Identifier: MIT

from .dependencies import Provider, dependency_provider
from .use import Depends, inject

__all__ = (
    "Depends",
    "Provider",
    "dependency_provider",
    "inject",
)
