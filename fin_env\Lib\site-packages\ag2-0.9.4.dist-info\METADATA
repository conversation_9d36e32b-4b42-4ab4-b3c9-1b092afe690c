Metadata-Version: 2.4
Name: ag2
Version: 0.9.4
Summary: A programming framework for agentic AI
Project-URL: Homepage, https://ag2.ai/
Project-URL: Documentation, https://docs.ag2.ai
Project-URL: Tracker, https://github.com/ag2ai/ag2/issues
Project-URL: Source, https://github.com/ag2ai/ag2
Project-URL: Discord, https://discord.gg/pAbnFJrkgZ
Author-email: <PERSON> & <PERSON> <<EMAIL>>
License-File: LICENSE
License-File: NOTICE.md
Keywords: ag2,ag2.ai,ag2ai,agent,agentic,ai,autogen
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: <3.14,>=3.9
Requires-Dist: anyio<5.0.0,>=3.0.0
Requires-Dist: asyncer==0.0.8
Requires-Dist: diskcache
Requires-Dist: docker
Requires-Dist: httpx<1,>=0.28.1
Requires-Dist: packaging
Requires-Dist: pydantic<3,>=2.6.1
Requires-Dist: python-dotenv
Requires-Dist: termcolor
Requires-Dist: tiktoken
Provides-Extra: anthropic
Requires-Dist: anthropic[vertex]>=0.23.1; extra == 'anthropic'
Provides-Extra: autobuild
Requires-Dist: chromadb; extra == 'autobuild'
Requires-Dist: huggingface-hub; extra == 'autobuild'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'autobuild'
Provides-Extra: bedrock
Requires-Dist: boto3>=1.34.149; extra == 'bedrock'
Provides-Extra: blendsearch
Requires-Dist: flaml[blendsearch]; extra == 'blendsearch'
Provides-Extra: browser-use
Requires-Dist: browser-use==0.1.37; extra == 'browser-use'
Provides-Extra: captainagent
Requires-Dist: chromadb; extra == 'captainagent'
Requires-Dist: huggingface-hub; extra == 'captainagent'
Requires-Dist: pandas; extra == 'captainagent'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'captainagent'
Provides-Extra: cerebras
Requires-Dist: cerebras-cloud-sdk>=1.0.0; extra == 'cerebras'
Provides-Extra: cohere
Requires-Dist: cohere>=5.13.5; extra == 'cohere'
Provides-Extra: commsagent-discord
Requires-Dist: discord-py<2.6,>=2.4.0; extra == 'commsagent-discord'
Provides-Extra: commsagent-slack
Requires-Dist: slack-sdk<3.40,>=3.33.0; extra == 'commsagent-slack'
Provides-Extra: commsagent-telegram
Requires-Dist: telethon<2,>=1.38.1; extra == 'commsagent-telegram'
Provides-Extra: cosmosdb
Requires-Dist: azure-cosmos>=4.2.0; extra == 'cosmosdb'
Provides-Extra: crawl4ai
Requires-Dist: crawl4ai<0.5,>=0.4.247; extra == 'crawl4ai'
Provides-Extra: deepseek
Requires-Dist: openai>=1.87.0; extra == 'deepseek'
Provides-Extra: dev
Requires-Dist: cairosvg; extra == 'dev'
Requires-Dist: codespell==2.4.1; extra == 'dev'
Requires-Dist: detect-secrets==1.5.0; extra == 'dev'
Requires-Dist: dirty-equals==0.9.0; extra == 'dev'
Requires-Dist: fastapi==0.115.12; extra == 'dev'
Requires-Dist: ipykernel==6.29.5; extra == 'dev'
Requires-Dist: jinja2==3.1.6; extra == 'dev'
Requires-Dist: mdx-include==1.4.2; extra == 'dev'
Requires-Dist: mike==2.1.3; extra == 'dev'
Requires-Dist: mkdocs-git-revision-date-localized-plugin==1.3.0; extra == 'dev'
Requires-Dist: mkdocs-glightbox==0.4.0; extra == 'dev'
Requires-Dist: mkdocs-literate-nav==0.6.2; extra == 'dev'
Requires-Dist: mkdocs-macros-plugin==1.3.7; extra == 'dev'
Requires-Dist: mkdocs-material==9.6.12; extra == 'dev'
Requires-Dist: mkdocs-minify-plugin==0.8.0; extra == 'dev'
Requires-Dist: mkdocs-redirects==1.2.2; extra == 'dev'
Requires-Dist: mkdocstrings[python]==0.29.1; extra == 'dev'
Requires-Dist: mock==5.2.0; extra == 'dev'
Requires-Dist: mypy==1.15.0; extra == 'dev'
Requires-Dist: nbclient==0.10.2; extra == 'dev'
Requires-Dist: nbconvert==7.16.6; extra == 'dev'
Requires-Dist: nbformat==5.10.4; extra == 'dev'
Requires-Dist: openai>=1.87.0; extra == 'dev'
Requires-Dist: pandas==2.2.3; extra == 'dev'
Requires-Dist: pdoc3==0.11.6; extra == 'dev'
Requires-Dist: pillow; extra == 'dev'
Requires-Dist: pre-commit==4.2.0; extra == 'dev'
Requires-Dist: pytest-asyncio==0.26.0; extra == 'dev'
Requires-Dist: pytest-cov==6.1.1; extra == 'dev'
Requires-Dist: pytest==8.3.5; extra == 'dev'
Requires-Dist: pyupgrade-directories==0.3.0; extra == 'dev'
Requires-Dist: pyyaml==6.0.2; extra == 'dev'
Requires-Dist: ruff==0.11.6; extra == 'dev'
Requires-Dist: termcolor==3.0.1; extra == 'dev'
Requires-Dist: toml==0.10.2; extra == 'dev'
Requires-Dist: typer==0.15.2; extra == 'dev'
Requires-Dist: uv==0.6.14; extra == 'dev'
Provides-Extra: docs
Requires-Dist: cairosvg; extra == 'docs'
Requires-Dist: jinja2==3.1.6; extra == 'docs'
Requires-Dist: mdx-include==1.4.2; extra == 'docs'
Requires-Dist: mike==2.1.3; extra == 'docs'
Requires-Dist: mkdocs-git-revision-date-localized-plugin==1.3.0; extra == 'docs'
Requires-Dist: mkdocs-glightbox==0.4.0; extra == 'docs'
Requires-Dist: mkdocs-literate-nav==0.6.2; extra == 'docs'
Requires-Dist: mkdocs-macros-plugin==1.3.7; extra == 'docs'
Requires-Dist: mkdocs-material==9.6.12; extra == 'docs'
Requires-Dist: mkdocs-minify-plugin==0.8.0; extra == 'docs'
Requires-Dist: mkdocs-redirects==1.2.2; extra == 'docs'
Requires-Dist: mkdocstrings[python]==0.29.1; extra == 'docs'
Requires-Dist: nbclient==0.10.2; extra == 'docs'
Requires-Dist: pdoc3==0.11.6; extra == 'docs'
Requires-Dist: pillow; extra == 'docs'
Requires-Dist: pyyaml==6.0.2; extra == 'docs'
Requires-Dist: termcolor==3.0.1; extra == 'docs'
Requires-Dist: typer==0.15.2; extra == 'docs'
Provides-Extra: duckduckgo
Requires-Dist: duckduckgo-search>=8.0.2; extra == 'duckduckgo'
Provides-Extra: flaml
Requires-Dist: flaml; extra == 'flaml'
Requires-Dist: numpy<2.0.0,>=1.24.0; (python_version < '3.13') and extra == 'flaml'
Requires-Dist: numpy>=2.1; (python_version >= '3.13') and extra == 'flaml'
Provides-Extra: gemini
Requires-Dist: google-api-core; extra == 'gemini'
Requires-Dist: google-auth; extra == 'gemini'
Requires-Dist: google-cloud-aiplatform; extra == 'gemini'
Requires-Dist: google-genai>=1.20.0; extra == 'gemini'
Requires-Dist: jsonschema; extra == 'gemini'
Requires-Dist: pillow; extra == 'gemini'
Provides-Extra: gemini-realtime
Requires-Dist: google-api-core; extra == 'gemini-realtime'
Requires-Dist: google-auth; extra == 'gemini-realtime'
Requires-Dist: google-cloud-aiplatform; extra == 'gemini-realtime'
Requires-Dist: google-genai>=1.20.0; extra == 'gemini-realtime'
Requires-Dist: jsonschema; extra == 'gemini-realtime'
Requires-Dist: pillow; extra == 'gemini-realtime'
Requires-Dist: websockets<16,>=14.0; extra == 'gemini-realtime'
Provides-Extra: google-api
Requires-Dist: google-api-python-client<3.0,>=2.163.0; extra == 'google-api'
Requires-Dist: google-auth-httplib2<0.3,>=0.2.0; extra == 'google-api'
Requires-Dist: google-auth-oauthlib<2.0,>=1.2.1; extra == 'google-api'
Provides-Extra: google-client
Requires-Dist: google-api-python-client<3.0,>=2.163.0; extra == 'google-client'
Provides-Extra: google-search
Requires-Dist: google-api-python-client<3.0,>=2.163.0; extra == 'google-search'
Provides-Extra: graph
Requires-Dist: matplotlib; extra == 'graph'
Requires-Dist: networkx; extra == 'graph'
Provides-Extra: graph-rag-falkor-db
Requires-Dist: falkordb>=1.0.10; extra == 'graph-rag-falkor-db'
Requires-Dist: graphrag-sdk==0.7.1; extra == 'graph-rag-falkor-db'
Provides-Extra: groq
Requires-Dist: groq>=0.9.0; extra == 'groq'
Provides-Extra: interop
Requires-Dist: crewai[tools]<1,>=0.76; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop'
Requires-Dist: langchain-community<1,>=0.3.12; extra == 'interop'
Requires-Dist: litellm<=1.67.1; extra == 'interop'
Requires-Dist: pydantic-ai==0.0.31; extra == 'interop'
Requires-Dist: weaviate-client<5,>=4; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop'
Provides-Extra: interop-crewai
Requires-Dist: crewai[tools]<1,>=0.76; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop-crewai'
Requires-Dist: litellm<=1.67.1; extra == 'interop-crewai'
Requires-Dist: weaviate-client<5,>=4; (python_version >= '3.10' and python_version < '3.13') and extra == 'interop-crewai'
Provides-Extra: interop-langchain
Requires-Dist: langchain-community<1,>=0.3.12; extra == 'interop-langchain'
Provides-Extra: interop-pydantic-ai
Requires-Dist: pydantic-ai==0.0.31; extra == 'interop-pydantic-ai'
Provides-Extra: jupyter-executor
Requires-Dist: ipykernel>=6.29.0; extra == 'jupyter-executor'
Requires-Dist: jupyter-client>=8.6.0; extra == 'jupyter-executor'
Requires-Dist: jupyter-kernel-gateway; extra == 'jupyter-executor'
Requires-Dist: requests; extra == 'jupyter-executor'
Requires-Dist: websocket-client; extra == 'jupyter-executor'
Provides-Extra: lint
Requires-Dist: codespell==2.4.1; extra == 'lint'
Requires-Dist: pyupgrade-directories==0.3.0; extra == 'lint'
Requires-Dist: ruff==0.11.6; extra == 'lint'
Provides-Extra: lmm
Requires-Dist: pillow; extra == 'lmm'
Requires-Dist: replicate; extra == 'lmm'
Provides-Extra: long-context
Requires-Dist: llmlingua<0.3; extra == 'long-context'
Provides-Extra: mathchat
Requires-Dist: sympy; extra == 'mathchat'
Requires-Dist: wolframalpha; extra == 'mathchat'
Provides-Extra: mcp
Requires-Dist: mcp>=1.9.4; (python_version >= '3.10') and extra == 'mcp'
Provides-Extra: mcp-proxy-gen
Requires-Dist: fastapi-code-generator>=0.5.4; extra == 'mcp-proxy-gen'
Requires-Dist: fastapi<1,>=0.112; extra == 'mcp-proxy-gen'
Requires-Dist: pyyaml; extra == 'mcp-proxy-gen'
Requires-Dist: requests; extra == 'mcp-proxy-gen'
Requires-Dist: typer; extra == 'mcp-proxy-gen'
Provides-Extra: mistral
Requires-Dist: mistralai>=1.0.1; extra == 'mistral'
Provides-Extra: neo4j
Requires-Dist: docx2txt==0.9; extra == 'neo4j'
Requires-Dist: llama-index-graph-stores-neo4j==0.4.6; extra == 'neo4j'
Requires-Dist: llama-index-readers-web==0.3.9; extra == 'neo4j'
Requires-Dist: llama-index<1,>=0.12; extra == 'neo4j'
Provides-Extra: ollama
Requires-Dist: fix-busted-json>=0.0.18; extra == 'ollama'
Requires-Dist: ollama>=0.4.7; extra == 'ollama'
Provides-Extra: openai
Requires-Dist: openai>=1.87.0; extra == 'openai'
Provides-Extra: openai-realtime
Requires-Dist: openai>=1.87.0; extra == 'openai-realtime'
Requires-Dist: openai[realtime]; extra == 'openai-realtime'
Provides-Extra: rag
Requires-Dist: chromadb<1,>=0.5; extra == 'rag'
Requires-Dist: docling<3,>=2.15.1; extra == 'rag'
Requires-Dist: llama-index-embeddings-huggingface==0.5.3; extra == 'rag'
Requires-Dist: llama-index-llms-langchain==0.6.1; extra == 'rag'
Requires-Dist: llama-index-vector-stores-chroma==0.4.1; extra == 'rag'
Requires-Dist: llama-index-vector-stores-mongodb==0.6.0; extra == 'rag'
Requires-Dist: llama-index<1,>=0.12; extra == 'rag'
Requires-Dist: requests<3,>=2.32.3; extra == 'rag'
Requires-Dist: selenium<5,>=4.28.1; extra == 'rag'
Requires-Dist: webdriver-manager==4.0.2; extra == 'rag'
Provides-Extra: redis
Requires-Dist: redis; extra == 'redis'
Provides-Extra: retrievechat
Requires-Dist: beautifulsoup4; extra == 'retrievechat'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat'
Requires-Dist: ipython; extra == 'retrievechat'
Requires-Dist: markdownify; extra == 'retrievechat'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat'
Requires-Dist: pypdf; extra == 'retrievechat'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'retrievechat'
Provides-Extra: retrievechat-couchbase
Requires-Dist: beautifulsoup4; extra == 'retrievechat-couchbase'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-couchbase'
Requires-Dist: couchbase>=4.3.0; extra == 'retrievechat-couchbase'
Requires-Dist: ipython; extra == 'retrievechat-couchbase'
Requires-Dist: markdownify; extra == 'retrievechat-couchbase'
Requires-Dist: numpy; extra == 'retrievechat-couchbase'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-couchbase'
Requires-Dist: pypdf; extra == 'retrievechat-couchbase'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'retrievechat-couchbase'
Provides-Extra: retrievechat-mongodb
Requires-Dist: beautifulsoup4; extra == 'retrievechat-mongodb'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-mongodb'
Requires-Dist: ipython; extra == 'retrievechat-mongodb'
Requires-Dist: markdownify; extra == 'retrievechat-mongodb'
Requires-Dist: numpy; extra == 'retrievechat-mongodb'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-mongodb'
Requires-Dist: pymongo>=4.0.0; extra == 'retrievechat-mongodb'
Requires-Dist: pypdf; extra == 'retrievechat-mongodb'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'retrievechat-mongodb'
Provides-Extra: retrievechat-pgvector
Requires-Dist: beautifulsoup4; extra == 'retrievechat-pgvector'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-pgvector'
Requires-Dist: ipython; extra == 'retrievechat-pgvector'
Requires-Dist: markdownify; extra == 'retrievechat-pgvector'
Requires-Dist: pgvector>=0.2.5; extra == 'retrievechat-pgvector'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-pgvector'
Requires-Dist: psycopg>=3.1.18; (platform_system == 'Linux') and extra == 'retrievechat-pgvector'
Requires-Dist: psycopg[binary]>=3.1.18; (platform_system == 'Windows' or platform_system == 'Darwin') and extra == 'retrievechat-pgvector'
Requires-Dist: pypdf; extra == 'retrievechat-pgvector'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'retrievechat-pgvector'
Provides-Extra: retrievechat-qdrant
Requires-Dist: beautifulsoup4; extra == 'retrievechat-qdrant'
Requires-Dist: chromadb==0.6.3; extra == 'retrievechat-qdrant'
Requires-Dist: fastembed>=0.3.1; extra == 'retrievechat-qdrant'
Requires-Dist: ipython; extra == 'retrievechat-qdrant'
Requires-Dist: markdownify; extra == 'retrievechat-qdrant'
Requires-Dist: protobuf==5.29.3; extra == 'retrievechat-qdrant'
Requires-Dist: pypdf; extra == 'retrievechat-qdrant'
Requires-Dist: qdrant-client; extra == 'retrievechat-qdrant'
Requires-Dist: sentence-transformers<=4.1.0; extra == 'retrievechat-qdrant'
Provides-Extra: tavily
Requires-Dist: tavily-python>=0.7.4; extra == 'tavily'
Provides-Extra: teachable
Requires-Dist: chromadb; extra == 'teachable'
Provides-Extra: test
Requires-Dist: dirty-equals==0.9.0; extra == 'test'
Requires-Dist: fastapi==0.115.12; extra == 'test'
Requires-Dist: ipykernel==6.29.5; extra == 'test'
Requires-Dist: mock==5.2.0; extra == 'test'
Requires-Dist: nbconvert==7.16.6; extra == 'test'
Requires-Dist: nbformat==5.10.4; extra == 'test'
Requires-Dist: pandas==2.2.3; extra == 'test'
Requires-Dist: pytest-asyncio==0.26.0; extra == 'test'
Requires-Dist: pytest-cov==6.1.1; extra == 'test'
Requires-Dist: pytest==8.3.5; extra == 'test'
Provides-Extra: together
Requires-Dist: together>=1.2; extra == 'together'
Provides-Extra: twilio
Requires-Dist: fastapi<1,>=0.115.0; extra == 'twilio'
Requires-Dist: twilio>=9.3.2; extra == 'twilio'
Requires-Dist: uvicorn<1,>=0.30.6; extra == 'twilio'
Provides-Extra: types
Requires-Dist: dirty-equals==0.9.0; extra == 'types'
Requires-Dist: fastapi==0.115.12; extra == 'types'
Requires-Dist: ipykernel==6.29.5; extra == 'types'
Requires-Dist: mock==5.2.0; extra == 'types'
Requires-Dist: mypy==1.15.0; extra == 'types'
Requires-Dist: nbconvert==7.16.6; extra == 'types'
Requires-Dist: nbformat==5.10.4; extra == 'types'
Requires-Dist: openai>=1.87.0; extra == 'types'
Requires-Dist: pandas==2.2.3; extra == 'types'
Requires-Dist: pytest-asyncio==0.26.0; extra == 'types'
Requires-Dist: pytest-cov==6.1.1; extra == 'types'
Requires-Dist: pytest==8.3.5; extra == 'types'
Provides-Extra: websockets
Requires-Dist: websockets<16,>=14.0; extra == 'websockets'
Provides-Extra: websurfer
Requires-Dist: beautifulsoup4; extra == 'websurfer'
Requires-Dist: markdownify; extra == 'websurfer'
Requires-Dist: pathvalidate; extra == 'websurfer'
Requires-Dist: pdfminer-six; extra == 'websurfer'
Provides-Extra: wikipedia
Requires-Dist: wikipedia-api<1.0,>=0.8.1; extra == 'wikipedia'
Description-Content-Type: text/markdown

<a name="readme-top"></a>

<p align="center">
  <!-- The image URL points to the GitHub-hosted content, ensuring it displays correctly on the PyPI website.-->
  <img src="https://raw.githubusercontent.com/ag2ai/ag2/27b37494a6f72b1f8050f6bd7be9a7ff232cf749/website/static/img/ag2.svg" width="150" title="hover text">
  <br>
  <br>
  <img src="https://img.shields.io/pypi/dm/ag2?label=PyPI%20downloads">
  <a href="https://badge.fury.io/py/autogen"><img src="https://badge.fury.io/py/autogen.svg"></a>
  <a href="https://github.com/ag2ai/ag2/actions/workflows/python-package.yml">
    <img src="https://github.com/ag2ai/ag2/actions/workflows/python-package.yml/badge.svg">
  </a>
  <img src="https://img.shields.io/badge/3.9%20%7C%203.10%20%7C%203.11%20%7C%203.12-blue">
  <a href="https://discord.gg/pAbnFJrkgZ">
    <img src="https://img.shields.io/discord/1153072414184452236?logo=discord&style=flat">
  </a>
  <a href="https://x.com/ag2oss">
    <img src="https://img.shields.io/twitter/url/https/twitter.com/cloudposse.svg?style=social&label=Follow%20%40ag2ai">
  </a>
</p>

<p align="center">
  <a href="https://docs.ag2.ai/">📚 Documentation</a> |
  <a href="https://github.com/ag2ai/build-with-ag2">💡 Examples</a> |
  <a href="https://docs.ag2.ai/latest/docs/contributor-guide/contributing">🤝 Contributing</a> |
  <a href="#related-papers">📝 Cite paper</a> |
  <a href="https://discord.gg/pAbnFJrkgZ">💬 Join Discord</a>
</p>

<p align="center">
AG2 was evolved from AutoGen. Fully open-sourced. We invite collaborators from all organizations to contribute.
</p>

# AG2: Open-Source AgentOS for AI Agents

AG2 (formerly AutoGen) is an open-source programming framework for building AI agents and facilitating cooperation among multiple agents to solve tasks. AG2 aims to streamline the development and research of agentic AI. It offers features such as agents capable of interacting with each other, facilitates the use of various large language models (LLMs) and tool use support, autonomous and human-in-the-loop workflows, and multi-agent conversation patterns.

The project is currently maintained by a [dynamic group of volunteers](MAINTAINERS.md) from several organizations. Contact project administrators Chi Wang and Qingyun Wu via [<EMAIL>](mailto:<EMAIL>) if you are interested in becoming a maintainer.

## Table of contents

- [AG2: Open-Source AgentOS for AI Agents](#ag2-open-source-agentos-for-ai-agents)
  - [Table of contents](#table-of-contents)
  - [Getting started](#getting-started)
    - [Installation](#installation)
    - [Setup your API keys](#setup-your-api-keys)
    - [Run your first agent](#run-your-first-agent)
  - [Example applications](#example-applications)
  - [Introduction of different agent concepts](#introduction-of-different-agent-concepts)
    - [Conversable agent](#conversable-agent)
    - [Human in the loop](#human-in-the-loop)
    - [Orchestrating multiple agents](#orchestrating-multiple-agents)
    - [Tools](#tools)
    - [Advanced agentic design patterns](#advanced-agentic-design-patterns)
  - [Announcements](#announcements)
  - [Contributors Wall](#contributors-wall)
  - [Code style and linting](#code-style-and-linting)
  - [Related papers](#related-papers)
  - [Cite the project](#cite-the-project)
  - [License](#license)

## Getting started

For a step-by-step walk through of AG2 concepts and code, see [Basic Concepts](https://docs.ag2.ai/latest/docs/user-guide/basic-concepts/installing-ag2/) in our documentation.

### Installation

AG2 requires **Python version >= 3.9, < 3.14**. AG2 is available via `ag2` (or its alias `autogen`) on PyPI.

```bash
pip install ag2[openai]
```

Minimal dependencies are installed by default. You can install extra options based on the features you need.

### Setup your API keys

To keep your LLM dependencies neat we recommend using the `OAI_CONFIG_LIST` file to store your API keys.

You can use the sample file `OAI_CONFIG_LIST_sample` as a template.

```json
[
  {
    "model": "gpt-4o",
    "api_key": "<your OpenAI API key here>"
  }
]
```

### Run your first agent

Create a script or a Jupyter Notebook and run your first agent.

```python
from autogen import AssistantAgent, UserProxyAgent, LLMConfig

llm_config = LLMConfig.from_json(path="OAI_CONFIG_LIST")


with llm_config:
    assistant = AssistantAgent("assistant")
user_proxy = UserProxyAgent("user_proxy", code_execution_config={"work_dir": "coding", "use_docker": False})
user_proxy.initiate_chat(assistant, message="Plot a chart of NVDA and TESLA stock price change YTD.")
# This initiates an automated chat between the two agents to solve the task
```

## Example applications

We maintain a dedicated repository with a wide range of applications to help you get started with various use cases or check out our collection of jupyter notebooks as a starting point.

- [Build with AG2](https://github.com/ag2ai/build-with-ag2)
- [Jupyter Notebooks](notebook)

## Introduction of different agent concepts

We have several agent concepts in AG2 to help you build your AI agents. We introduce the most common ones here.

- **Conversable Agent**: Agents that are able to send messages, receive messages and generate replies using GenAI models, non-GenAI tools, or human inputs.
- **Human in the loop**: Add human input to the conversation
- **Orchestrating multiple agents**: Users can orchestrate multiple agents with built-in conversation patterns such as swarms, group chats, nested chats, sequential chats or customize the orchestration by registering custom reply methods.
- **Tools**: Programs that can be registered, invoked and executed by agents
- **Advanced Concepts**: AG2 supports more concepts such as structured outputs, rag, code execution, etc.

### Conversable agent

The [ConversableAgent](https://docs.ag2.ai/latest/docs/api-reference/autogen/ConversableAgent) is the fundamental building block of AG2, designed to enable seamless communication between AI entities. This core agent type handles message exchange and response generation, serving as the base class for all agents in the framework.

In the example below, we'll create a simple information validation workflow with two specialized agents that communicate with each other:

Note: Before running this code, make sure to set your `OPENAI_API_KEY` as an environment variable. This example uses `gpt-4o-mini`, but you can replace it with any other [model](https://docs.ag2.ai/latest/docs/user-guide/models/amazon-bedrock) supported by AG2.

```python
# 1. Import ConversableAgent class
from autogen import ConversableAgent, LLMConfig

# 2. Define our LLM configuration for OpenAI's GPT-4o mini
#    uses the OPENAI_API_KEY environment variable
llm_config = LLMConfig(api_type="openai", model="gpt-4o-mini")


# 3. Create our LLM agent
with llm_config:
  # Create an AI agent
  assistant = ConversableAgent(
      name="assistant",
      system_message="You are an assistant that responds concisely.",
  )

  # Create another AI agent
  fact_checker = ConversableAgent(
      name="fact_checker",
      system_message="You are a fact-checking assistant.",
  )

# 4. Start the conversation
assistant.initiate_chat(
    recipient=fact_checker,
    message="What is AG2?",
    max_turns=2
)
```

### Human in the loop

Human oversight is crucial for many AI workflows, especially when dealing with critical decisions, creative tasks, or situations requiring expert judgment. AG2 makes integrating human feedback seamless through its human-in-the-loop functionality.
You can configure how and when human input is solicited using the `human_input_mode` parameter:

- `ALWAYS`: Requires human input for every response
- `NEVER`: Operates autonomously without human involvement
- `TERMINATE`: Only requests human input to end conversations

For convenience, AG2 provides the specialized `UserProxyAgent` class that automatically sets `human_input_mode` to `ALWAYS` and supports code execution:

Note: Before running this code, make sure to set your `OPENAI_API_KEY` as an environment variable. This example uses `gpt-4o-mini`, but you can replace it with any other [model](https://docs.ag2.ai/latest/docs/user-guide/models/amazon-bedrock) supported by AG2.

```python
# 1. Import ConversableAgent and UserProxyAgent classes
from autogen import ConversableAgent, UserProxyAgent, LLMConfig

# 2. Define our LLM configuration for OpenAI's GPT-4o mini
#    uses the OPENAI_API_KEY environment variable
llm_config = LLMConfig(api_type="openai", model="gpt-4o-mini")


# 3. Create our LLM agent
with llm_config:
  assistant = ConversableAgent(
      name="assistant",
      system_message="You are a helpful assistant.",
  )

# 4. Create a human agent with manual input mode
human = ConversableAgent(
    name="human",
    human_input_mode="ALWAYS"
)
# or
human = UserProxyAgent(name="human", code_execution_config={"work_dir": "coding", "use_docker": False})

# 5. Start the chat
human.initiate_chat(
    recipient=assistant,
    message="Hello! What's 2 + 2?"
)

```

### Orchestrating multiple agents

AG2 enables sophisticated multi-agent collaboration through flexible orchestration patterns, allowing you to create dynamic systems where specialized agents work together to solve complex problems.

The framework offers both custom orchestration and several built-in collaboration patterns including `GroupChat` and `Swarm`.

Here's how to implement a collaborative team for curriculum development using GroupChat:

Note: Before running this code, make sure to set your `OPENAI_API_KEY` as an environment variable. This example uses `gpt-4o-mini`, but you can replace it with any other [model](https://docs.ag2.ai/latest/docs/user-guide/models/amazon-bedrock) supported by AG2.

```python
from autogen import ConversableAgent, GroupChat, GroupChatManager, LLMConfig

# Put your key in the OPENAI_API_KEY environment variable
llm_config = LLMConfig(api_type="openai", model="gpt-4o-mini")

planner_message = """You are a classroom lesson agent.
Given a topic, write a lesson plan for a fourth grade class.
Use the following format:
<title>Lesson plan title</title>
<learning_objectives>Key learning objectives</learning_objectives>
<script>How to introduce the topic to the kids</script>
"""

reviewer_message = """You are a classroom lesson reviewer.
You compare the lesson plan to the fourth grade curriculum and provide a maximum of 3 recommended changes.
Provide only one round of reviews to a lesson plan.
"""

# 1. Add a separate 'description' for our planner and reviewer agents
planner_description = "Creates or revises lesson plans."

reviewer_description = """Provides one round of reviews to a lesson plan
for the lesson_planner to revise."""

with llm_config:
    lesson_planner = ConversableAgent(
        name="planner_agent",
        system_message=planner_message,
        description=planner_description,
    )

    lesson_reviewer = ConversableAgent(
        name="reviewer_agent",
        system_message=reviewer_message,
        description=reviewer_description,
    )

# 2. The teacher's system message can also be used as a description, so we don't define it
teacher_message = """You are a classroom teacher.
You decide topics for lessons and work with a lesson planner.
and reviewer to create and finalise lesson plans.
When you are happy with a lesson plan, output "DONE!".
"""

with llm_config:
    teacher = ConversableAgent(
        name="teacher_agent",
        system_message=teacher_message,
        # 3. Our teacher can end the conversation by saying DONE!
        is_termination_msg=lambda x: "DONE!" in (x.get("content", "") or "").upper(),
    )

# 4. Create the GroupChat with agents and selection method
groupchat = GroupChat(
    agents=[teacher, lesson_planner, lesson_reviewer],
    speaker_selection_method="auto",
    messages=[],
)

# 5. Our GroupChatManager will manage the conversation and uses an LLM to select the next agent
manager = GroupChatManager(
    name="group_manager",
    groupchat=groupchat,
    llm_config=llm_config,
)

# 6. Initiate the chat with the GroupChatManager as the recipient
teacher.initiate_chat(
    recipient=manager,
    message="Today, let's introduce our kids to the solar system."
)
```

When executed, this code creates a collaborative system where the teacher initiates the conversation, and the lesson planner and reviewer agents work together to create and refine a lesson plan. The GroupChatManager orchestrates the conversation, selecting the next agent to respond based on the context of the discussion.

For workflows requiring more structured processes, explore the Group Chat pattern in the detailed [documentation](https://docs.ag2.ai/latest/docs/user-guide/advanced-concepts/orchestration/group-chat/introduction).

### Tools

Agents gain significant utility through tools as they provide access to external data, APIs, and functionality.

Note: Before running this code, make sure to set your `OPENAI_API_KEY` as an environment variable. This example uses `gpt-4o-mini`, but you can replace it with any other [model](https://docs.ag2.ai/latest/docs/user-guide/models/amazon-bedrock) supported by AG2.

```python
from datetime import datetime
from typing import Annotated

from autogen import ConversableAgent, register_function, LLMConfig

# Put your key in the OPENAI_API_KEY environment variable
llm_config = LLMConfig(api_type="openai", model="gpt-4o-mini")

# 1. Our tool, returns the day of the week for a given date
def get_weekday(date_string: Annotated[str, "Format: YYYY-MM-DD"]) -> str:
    date = datetime.strptime(date_string, "%Y-%m-%d")
    return date.strftime("%A")

# 2. Agent for determining whether to run the tool
with llm_config:
    date_agent = ConversableAgent(
        name="date_agent",
        system_message="You get the day of the week for a given date.",
    )

# 3. And an agent for executing the tool
executor_agent = ConversableAgent(
    name="executor_agent",
    human_input_mode="NEVER",
)

# 4. Registers the tool with the agents, the description will be used by the LLM
register_function(
    get_weekday,
    caller=date_agent,
    executor=executor_agent,
    description="Get the day of the week for a given date",
)

# 5. Two-way chat ensures the executor agent follows the suggesting agent
chat_result = executor_agent.initiate_chat(
    recipient=date_agent,
    message="I was born on the 25th of March 1995, what day was it?",
    max_turns=2,
)

print(chat_result.chat_history[-1]["content"])
```

### Advanced agentic design patterns

AG2 supports more advanced concepts to help you build your AI agent workflows. You can find more information in the documentation.

- [Structured Output](https://docs.ag2.ai/latest/docs/user-guide/basic-concepts/structured-outputs)
- [Ending a conversation](https://docs.ag2.ai/latest/docs/user-guide/advanced-concepts/orchestration/ending-a-chat/)
- [Retrieval Augmented Generation (RAG)](https://docs.ag2.ai/latest/docs/user-guide/advanced-concepts/rag/)
- [Code Execution](https://docs.ag2.ai/latest/docs/user-guide/advanced-concepts/code-execution)
- [Tools with Secrets](https://docs.ag2.ai/latest/docs/user-guide/advanced-concepts/tools/tools-with-secrets/)

## Announcements

🔥 🎉 **Nov 11, 2024:** We are evolving AutoGen into **AG2**!
A new organization [AG2AI](https://github.com/ag2ai) is created to host the development of AG2 and related projects with open governance. Check [AG2's new look](https://ag2.ai/).

📄 **License:**
We adopt the Apache 2.0 license from v0.3. This enhances our commitment to open-source collaboration while providing additional protections for contributors and users alike.

🎉 May 29, 2024: DeepLearning.ai launched a new short course [AI Agentic Design Patterns with AutoGen](https://www.deeplearning.ai/short-courses/ai-agentic-design-patterns-with-autogen), made in collaboration with Microsoft and Penn State University, and taught by AutoGen creators [Chi Wang](https://github.com/sonichi) and [Qingyun Wu](https://github.com/qingyun-wu).

🎉 May 24, 2024: Foundation Capital published an article on [Forbes: The Promise of Multi-Agent AI](https://www.forbes.com/sites/joannechen/2024/05/24/the-promise-of-multi-agent-ai/?sh=2c1e4f454d97) and a video [AI in the Real World Episode 2: Exploring Multi-Agent AI and AutoGen with Chi Wang](https://www.youtube.com/watch?v=RLwyXRVvlNk).

🎉 Apr 17, 2024: Andrew Ng cited AutoGen in [The Batch newsletter](https://www.deeplearning.ai/the-batch/issue-245/) and [What's next for AI agentic workflows](https://youtu.be/sal78ACtGTc?si=JduUzN_1kDnMq0vF) at Sequoia Capital's AI Ascent (Mar 26).

[More Announcements](announcements.md)

## Contributors Wall

<a href="https://github.com/ag2ai/ag2/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=ag2ai/ag2&max=204" />
</a>

## Code style and linting

This project uses pre-commit hooks to maintain code quality. Before contributing:

1. Install pre-commit:

```bash
pip install pre-commit
pre-commit install
```

2. The hooks will run automatically on commit, or you can run them manually:

```bash
pre-commit run --all-files
```

## Related papers

- [AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation](https://arxiv.org/abs/2308.08155)

- [EcoOptiGen: Hyperparameter Optimization for Large Language Model Generation Inference](https://arxiv.org/abs/2303.04673)

- [MathChat: Converse to Tackle Challenging Math Problems with LLM Agents](https://arxiv.org/abs/2306.01337)

- [AgentOptimizer: Offline Training of Language Model Agents with Functions as Learnable Weights](https://arxiv.org/pdf/2402.11359)

- [StateFlow: Enhancing LLM Task-Solving through State-Driven Workflows](https://arxiv.org/abs/2403.11322)

## Cite the project

```
@software{AG2_2024,
author = {Chi Wang and Qingyun Wu and the AG2 Community},
title = {AG2: Open-Source AgentOS for AI Agents},
year = {2024},
url = {https://github.com/ag2ai/ag2},
note = {Available at https://docs.ag2.ai/},
version = {latest}
}
```

## License

This project is licensed under the [Apache License, Version 2.0 (Apache-2.0)](./LICENSE).

This project is a spin-off of [AutoGen](https://github.com/microsoft/autogen) and contains code under two licenses:

- The original code from https://github.com/microsoft/autogen is licensed under the MIT License. See the [LICENSE_original_MIT](./license_original/LICENSE_original_MIT) file for details.

- Modifications and additions made in this fork are licensed under the Apache License, Version 2.0. See the [LICENSE](./LICENSE) file for the full license text.

We have documented these changes for clarity and to ensure transparency with our user and contributor community. For more details, please see the [NOTICE](./NOTICE.md) file.
