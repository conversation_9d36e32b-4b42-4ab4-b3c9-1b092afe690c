# Vector Store Setup for RAG-RBAC System
# FinSolve Technologies - AutoGen Multi-Agent Chatbot
# Notebook 03: Vector Store Setup & Embedding Management

import os
import sys
from pathlib import Path
import json
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
import asyncio
from concurrent.futures import ThreadPoolExecutor
import logging

# Vector stores
import chromadb
from chromadb.config import Settings
import qdrant_client
from qdrant_client.models import Distance, VectorParams, PointStruct
from qdrant_client.http import models

# Embeddings and text processing
from langchain_community.embeddings import OpenAIEmbeddings, HuggingFaceEmbeddings
from langchain_community.vectorstores import Chroma, Qdrant
from langchain.schema import Document

# AutoGen 0.4 imports
from autogen import ConversableAgent

# Import from previous notebooks (assuming they're in the same directory)
try:
    from model_client import ModelClient, ModelRouter  # From Notebook 01
    print("✅ Successfully imported ModelClient from Notebook 01")
except ImportError:
    print("⚠️  Could not import ModelClient - please ensure model_client.py is available")
    ModelClient = None

print("🔍 FinSolve Technologies - Vector Store Setup & RAG Integration")
print("=" * 70)

# ===============================
# 1. VECTOR STORE CONFIGURATION
# ===============================

class VectorStoreConfig: """Configuration for different vector store backends"""
    
    # Embedding models configuration
    EMBEDDING_MODELS = {
    "openai_ada": {
        "model": "text-embedding-ada-002",
        "dimensions": 1536,
        "provider": "openai",
        "cost_per_1k": 0.0001
    },
    "openai_3_large": {
        "model": "text-embedding-3-large",
        "dimensions": 3072,
        "provider": "openai",
        "cost_per_1k": 0.00013
    },
    "openai_3_small": {
        "model": "text-embedding-3-small",
        "dimensions": 1536,
        "provider": "openai",
        "cost_per_1k": 0.00002
    },
    "huggingface_mpnet": {
        "model": "sentence-transformers/all-mpnet-base-v2",
        "dimensions": 768,
        "provider": "huggingface",
        "cost_per_1k": 0.0  # Free
    },
    "huggingface_minilm": {
        "model": "sentence-transformers/all-MiniLM-L6-v2",
        "dimensions": 384,
        "provider": "huggingface",
        "cost_per_1k": 0.0  # Free
    }
}
    
    # Vector store backends
    VECTOR_STORES = {
    "qdrant": {
        "host": "localhost",
        "port": 6333,
        "collection_name": "finsolve_documents",
        "distance_metric": Distance.COSINE
    },
    "chroma": {
        "persist_directory": "./data/vector_store/chroma",
        "collection_name": "finsolve_documents"
    }
}

class UserRoles(Enum): """User roles for RBAC system"""
    FINANCE_TEAM = "finance_team"
    MARKETING_TEAM = "marketing_team"
    HR_TEAM = "hr_team"
    ENGINEERING_TEAM = "engineering_team"
    C_LEVEL_EXECUTIVES = "c_level_executives"
    GENERAL_EMPLOYEES = "general_employees"

@dataclass
class VectorSearchResult: """Enhanced search result with RBAC information"""
    document_id: str
    content: str
    metadata: Dict
    score: float
    department: str
    access_roles: List[str
]
    filename: str
    chunk_info: str
    
    def is_accessible_by_role(self, user_role: UserRoles) -> bool: """Check if this result is accessible by the given role"""
        return user_role.value in self.access_roles
    
    def to_autogen_format(self) -> Dict: """Convert to AutoGen agent-friendly format"""
        return {
    "content": self.content,
    "source": self.filename,
    "department": self.department,
    "score": self.score,
    "chunk_info": self.chunk_info,
    "metadata": self.metadata
}

# ===============================
# 2. EMBEDDING MANAGER
# ===============================

class EmbeddingManager: """Manages embeddings using ModelClient integration"""
    
    def __init__(self, model_client: Optional[ModelClient
] = None, embedding_model: str = "openai_3_large"):
        self.model_client = model_client
        self.embedding_model = embedding_model
        self.config = VectorStoreConfig.EMBEDDING_MODELS[embedding_model
]
        
        # Initialize embedding client
        self._initialize_embedding_client()
        
        print(f"🧠 EmbeddingManager initialized with {embedding_model}")
        print(f"📐 Dimensions: {self.config['dimensions']}")
        print(f"💰 Cost per 1K tokens: ${self.config['cost_per_1k']}")
    
    def _initialize_embedding_client(self): """Initialize the appropriate embedding client"""
        if self.config[
    "provider"
] == "openai":
            if self.model_client:
                # Use ModelClient for OpenAI embeddings
                self.embedding_client = "model_client"  # We'll use model_client directly
                print("✅ Using ModelClient for OpenAI embeddings")
            else:
                # Fallback to direct OpenAI
                self.embedding_client = OpenAIEmbeddings(
                    model=self.config[
    "model"
],
                    openai_api_key=os.getenv("OPENAI_API_KEY")
                )
                print("✅ Using direct OpenAI embeddings")
        
        elif self.config[
    "provider"
] == "huggingface":
            self.embedding_client = HuggingFaceEmbeddings(
                model_name=self.config[
    "model"
]
            )
            print("✅ Using HuggingFace embeddings")
    
    async def embed_text(self, text: str) -> List[float
]: """Generate embedding for a single text"""
        try:
            if self.config[
    "provider"
] == "openai" and self.model_client:
                # Use ModelClient for embeddings
                response = await self.model_client.get_embedding(
                    text=text,
                    model=self.config[
    "model"
]
                )
                return response
            else:
                # Use LangChain embedding client
                if hasattr(self.embedding_client, 'aembed_query'):
                    return await self.embedding_client.aembed_query(text)
                else:
                    return self.embedding_client.embed_query(text)
        
        except Exception as e:
            print(f"❌ Error generating embedding: {str(e)}")
            # Return zero vector as fallback
            return [
    0.0
] * self.config[
    "dimensions"
]
    
    async def embed_documents(self, documents: List[Document
], batch_size: int = 50) -> List[Tuple[Document, List[float
        ]
    ]
]: """Generate embeddings for multiple documents with batching"""
        print(f"🔄 Generating embeddings for {len(documents)} documents...")
        
        embedded_docs = []
        total_tokens = 0
        
        # Process in batches
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size
]
            batch_start_time = time.time()
            
            print(f"📦 Processing batch {i//batch_size + 1}/{(len(documents) + batch_size - 1)//batch_size}")
            
            # Generate embeddings for batch
            batch_embeddings = []
            for doc in batch:
                # Combine content with key metadata for better embeddings
                embedding_text = self._prepare_text_for_embedding(doc)
                embedding = await self.embed_text(embedding_text)
                batch_embeddings.append((doc, embedding))
                
                # Estimate tokens (rough approximation)
                total_tokens += len(embedding_text.split())
            
            embedded_docs.extend(batch_embeddings)
            
            batch_time = time.time() - batch_start_time
            print(f"  ⏱️  Batch completed in {batch_time:.2f}s")
            
            # Small delay to respect rate limits
            if i + batch_size < len(documents):
                await asyncio.sleep(0.1)
        
        estimated_cost = (total_tokens / 1000) * self.config[
    "cost_per_1k"
]
        print(f"📊 Embedding generation completed:")
        print(f"  📄 Documents: {len(documents)}")
        print(f"  🔢 Total tokens: {total_tokens:,}")
        print(f"  💰 Estimated cost: ${estimated_cost:.4f}")
        
        return embedded_docs
    
    def _prepare_text_for_embedding(self, doc: Document) -> str: """Prepare document text for embedding with metadata enrichment"""
        metadata = doc.metadata
        
        # Combine content with important metadata
        enriched_parts = [
            f"Department: {metadata.get('department', 'unknown')}",
            f"Document: {metadata.get('filename', 'unknown')}",
            f"Keywords: {', '.join(metadata.get('keywords', [])[:5])}",  # Top 5 keywords
            doc.page_content
]
        
        return " | ".join(enriched_parts)

# ===============================
# 3. VECTOR STORE IMPLEMENTATIONS
# ===============================

class QdrantVectorStore: """Qdrant vector store implementation with RBAC support"""
    
    def __init__(self, host: str = "localhost", port: int = 6333, collection_name: str = "finsolve_documents"):
        self.host = host
        self.port = port
        self.collection_name = collection_name
        
        try:
            self.client = qdrant_client.QdrantClient(host=host, port=port)
            print(f"✅ Connected to Qdrant at {host}:{port}")
        except Exception as e:
            print(f"⚠️  Could not connect to Qdrant: {str(e)}")
            print("💡 To start Qdrant locally: docker run -p 6333:6333 qdrant/qdrant")
            self.client = None
    
    def create_collection(self, vector_size: int, distance_metric: Distance = Distance.COSINE): """Create a new collection in Qdrant"""
        if not self.client:
            return False
        
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_names = [col.name for col in collections
]
            
            if self.collection_name in collection_names:
                print(f"📁 Collection '{self.collection_name}' already exists")
                return True
            
            # Create new collection
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=vector_size, distance=distance_metric)
            )
            print(f"✅ Created collection '{self.collection_name}' with {vector_size} dimensions")
            return True
        
        except Exception as e:
            print(f"❌ Error creating collection: {str(e)}")
            return False
    
    def add_documents(self, embedded_docs: List[Tuple[Document, List[float
        ]
    ]
]) -> bool: """Add embedded documents to Qdrant"""
        if not self.client:
            return False
        
        print(f"📥 Adding {len(embedded_docs)} documents to Qdrant...")
        
        points = []
        for i, (doc, embedding) in enumerate(embedded_docs):
            # Create point with enhanced payload
            point = PointStruct(
                id=i,
                vector=embedding,
                payload={
    "content": doc.page_content,
    "filename": doc.metadata.get("filename",
    "unknown"),
    "department": doc.metadata.get("department",
    "unknown"),
    "access_roles": doc.metadata.get("access_roles",
    []),
    "sensitivity_level": doc.metadata.get("sensitivity_level",
    "normal"),
    "keywords": doc.metadata.get("keywords",
    []),
    "chunk_index": doc.metadata.get("chunk_index",
    0),
    "total_chunks": doc.metadata.get("total_chunks",
    1),
    "document_hash": doc.metadata.get("document_hash",
    ""),
    "last_modified": doc.metadata.get("last_modified",
    ""),
    "file_size": doc.metadata.get("file_size",
    0)
}
            )
            points.append(point)
        
        try:
            # Batch upload
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size
]
                self.client.upsert(
                    collection_name=self.collection_name,
                    points=batch
                )
                print(f"  📦 Uploaded batch {i//batch_size + 1}/{(len(points) + batch_size - 1)//batch_size}")
            
            print(f"✅ Successfully added {len(embedded_docs)} documents to Qdrant")
            return True
        
        except Exception as e:
            print(f"❌ Error adding documents: {str(e)}")
            return False
    
    def search(self, query_vector: List[float
], user_role: UserRoles, limit: int = 5) -> List[VectorSearchResult
]: """Search vectors with RBAC filtering"""
        if not self.client:
            return []
        
        try:
            # Search with larger limit to allow for RBAC filtering
            search_limit = min(limit * 3,
50)  # Search more to filter by role
            
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=search_limit
            )
            
            # Convert to VectorSearchResult and filter by role
            filtered_results = []
            for result in search_results:
                search_result = VectorSearchResult(
                    document_id=str(result.id),
                    content=result.payload[
    "content"
],
                    metadata=result.payload,
                    score=result.score,
                    department=result.payload.get("department",
"unknown"),
                    access_roles=result.payload.get("access_roles",
[]),
                    filename=result.payload.get("filename",
"unknown"),
                    chunk_info=f"{result.payload.get('chunk_index', 0) + 1}/{result.payload.get('total_chunks', 1)}"
                )
                
                # Apply RBAC filtering
                if search_result.is_accessible_by_role(user_role):
                    filtered_results.append(search_result)
                
                # Stop when we have enough results
                if len(filtered_results) >= limit:
                    break
            
            print(f"🔍 Found {len(filtered_results)} accessible results for {user_role.value}")
            return filtered_results
        
        except Exception as e:
            print(f"❌ Error searching: {str(e)}")
            return []

class ChromaVectorStore: """Chroma vector store implementation with RBAC support"""
    
    def __init__(self, persist_directory: str = "./data/vector_store/chroma", collection_name: str = "finsolve_documents"):
        self.persist_directory = Path(persist_directory)
        self.collection_name = collection_name
        
        # Create directory if it doesn't exist
        self.persist_directory.mkdir(parents=True, exist_ok=True)
        
        try:
            self.client = chromadb.PersistentClient(path=str(self.persist_directory))
            self.collection = None
            print(f"✅ Initialized Chroma at {persist_directory}")
        except Exception as e:
            print(f"❌ Error initializing Chroma: {str(e)}")
            self.client = None
    
    def create_collection(self, vector_size: int): """Create or get a collection in Chroma"""
        if not self.client:
            return False
        
        try:
            # Try to get existing collection
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                print(f"📁 Using existing collection '{self.collection_name}'")
            except:
                # Create new collection
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={
    "description": "FinSolve Technologies documents with RBAC"
}
                )
                print(f"✅ Created new collection '{self.collection_name}'")
            
            return True
        
        except Exception as e:
            print(f"❌ Error with collection: {str(e)}")
            return False
    
    def add_documents(self, embedded_docs: List[Tuple[Document, List[float
        ]
    ]
]) -> bool: """Add embedded documents to Chroma"""
        if not self.collection:
            return False
        
        print(f"📥 Adding {len(embedded_docs)} documents to Chroma...")
        
        # Prepare data for Chroma
        ids = []
        embeddings = []
        documents = []
        metadatas = []
        
        for i, (doc, embedding) in enumerate(embedded_docs):
            ids.append(f"doc_{i}")
            embeddings.append(embedding)
            documents.append(doc.page_content)
            
            # Prepare metadata (Chroma requires string values)
            metadata = {
    "filename": doc.metadata.get("filename",
    "unknown"),
    "department": doc.metadata.get("department",
    "unknown"),
    "access_roles": ",".join(doc.metadata.get("access_roles",
    [])),
    "sensitivity_level": doc.metadata.get("sensitivity_level",
    "normal"),
    "keywords": ",".join(doc.metadata.get("keywords",
    [])[
        : 10
    ]),  # Limit keywords
                "chunk_index": str(doc.metadata.get("chunk_index",
    0)),
    "total_chunks": str(doc.metadata.get("total_chunks",
    1)),
    "document_hash": doc.metadata.get("document_hash",
    ""),
    "file_size": str(doc.metadata.get("file_size",
    0))
}
            metadatas.append(metadata)
        
        try:
            # Add to collection in batches
            batch_size = 100
            for i in range(0, len(ids), batch_size):
                end_idx = min(i + batch_size, len(ids))
                
                self.collection.add(
                    embeddings=embeddings[i:end_idx
],
                    documents=documents[i:end_idx
],
                    metadatas=metadatas[i:end_idx
],
                    ids=ids[i:end_idx
]
                )
                print(f"  📦 Added batch {i//batch_size + 1}/{(len(ids) + batch_size - 1)//batch_size}")
            
            print(f"✅ Successfully added {len(embedded_docs)} documents to Chroma")
            return True
        
        except Exception as e:
            print(f"❌ Error adding documents: {str(e)}")
            return False
    
    def search(self, query_vector: List[float
], user_role: UserRoles, limit: int = 5) -> List[VectorSearchResult
]: """Search vectors with RBAC filtering"""
        if not self.collection:
            return []
        
        try:
            # Search with larger limit for RBAC filtering
            search_limit = min(limit * 3,
50)
            
            results = self.collection.query(
                query_embeddings=[query_vector
],
                n_results=search_limit,
                include=['embeddings', 'documents', 'metadatas', 'distances'
]
            )
            
            # Convert to VectorSearchResult and filter by role
            filtered_results = []
            
            if results['documents'
] and results['documents'
][
    0
]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'
][
    0
],
                    results['metadatas'
][
    0
],
                    results['distances'
][
    0
]
                )):
                    # Parse access roles
                    access_roles = metadata.get('access_roles', '').split(',')
                    access_roles = [role.strip() for role in access_roles if role.strip()
]
                    
                    search_result = VectorSearchResult(
                        document_id=results['ids'
][
    0
][i
] if 'ids' in results else str(i),
                        content=doc,
                        metadata=metadata,
                        score=1.0 - distance,  # Convert distance to similarity score
                        department=metadata.get("department",
"unknown"),
                        access_roles=access_roles,
                        filename=metadata.get("filename",
"unknown"),
                        chunk_info=f"{int(metadata.get('chunk_index', 0)) + 1}/{metadata.get('total_chunks', 1)}"
                    )
                    
                    # Apply RBAC filtering
                    if search_result.is_accessible_by_role(user_role):
                        filtered_results.append(search_result)
                    
                    # Stop when we have enough results
                    if len(filtered_results) >= limit:
                        break
            
            print(f"🔍 Found {len(filtered_results)} accessible results for {user_role.value}")
            return filtered_results
        
        except Exception as e:
            print(f"❌ Error searching: {str(e)}")
            return []

# ===============================
# 4. UNIFIED VECTOR STORE MANAGER
# ===============================

class VectorStoreManager: """Unified manager for different vector store backends"""
    
    def __init__(self, embedding_manager: EmbeddingManager, backend: str = "chroma"):
        self.embedding_manager = embedding_manager
        self.backend = backend
        
        # Initialize appropriate vector store
        if backend == "qdrant":
            config = VectorStoreConfig.VECTOR_STORES[
    "qdrant"
]
            self.vector_store = QdrantVectorStore(
                host=config[
    "host"
],
                port=config[
    "port"
],
                collection_name=config[
    "collection_name"
]
            )
        elif backend == "chroma":
            config = VectorStoreConfig.VECTOR_STORES[
    "chroma"
]
            self.vector_store = ChromaVectorStore(
                persist_directory=config[
    "persist_directory"
],
                collection_name=config[
    "collection_name"
]
            )
        else:
            raise ValueError(f"Unsupported backend: {backend}")
        
        print(f"🗄️  VectorStoreManager initialized with {backend} backend")
    
    async def setup_and_populate(self, documents: List[Document
]) -> bool: """Setup vector store and populate with documents"""
        print(f"\n🚀 Setting up vector store with {len(documents)} documents...")
        
        # Generate embeddings
        embedded_docs = await self.embedding_manager.embed_documents(documents)
        
        if not embedded_docs:
            print("❌ No embeddings generated")
            return False
        
        # Create collection
        vector_size = self.embedding_manager.config[
    "dimensions"
]
        
        if self.backend == "qdrant":
            success = self.vector_store.create_collection(vector_size, Distance.COSINE)
        else:  # chroma
            success = self.vector_store.create_collection(vector_size)
        
        if not success:
            print("❌ Failed to create collection")
            return False
        
        # Add documents
        success = self.vector_store.add_documents(embedded_docs)
        
        if success:
            print("✅ Vector store setup completed successfully!")
        else:
            print("❌ Failed to add documents to vector store")
        
        return success
    
    async def search(self, query: str, user_role: UserRoles, limit: int = 5) -> List[VectorSearchResult
]: """Search the vector store with RBAC filtering"""
        # Generate query embedding
        query_embedding = await self.embedding_manager.embed_text(query)
        
        if not query_embedding or all(x == 0 for x in query_embedding):
            print("❌ Failed to generate query embedding")
            return []
        
        # Search vector store
        return self.vector_store.search(query_embedding, user_role, limit)
    
    def create_autogen_search_tool(self, user_role: UserRoles): """Create an AutoGen agent tool for vector search"""
        
        async def vector_search_tool(query: str, max_results: int = 5) -> str: """
            Perform semantic search on company documents.
            
            Args:
                query: Search query string
                max_results: Maximum number of results to return
            
            Returns:
                Formatted search results with source attribution
            """
            try:
                # Perform vector search
                results = await self.search(query, user_role, max_results)
                
                if not results:
                    return f"No relevant documents found for query: '{query}' (Role: {user_role.value})"
                
                # Format results for AutoGen agent
                formatted_results = []
                for result in results:
                    formatted_result = f"**Source:** {result.filename} ({result.department})\n"
                    formatted_result += f"**Relevance Score:** {result.score:.3f}\n"
                    formatted_result += f"**Content:** {result.content[:400]}...\n"
                    formatted_result += f"**Chunk:** {result.chunk_info}\n"
                    formatted_results.append(formatted_result)
                
                search_summary = f"Found {len(results)} relevant documents for '{query}':\n\n"
                return search_summary + "\n---\n".join(formatted_results)
            
            except Exception as e:
                return f"Error performing search: {str(e)}"
        
        return vector_search_tool

# ===============================
# 5. TESTING & VALIDATION
# ===============================

async def test_vector_search_functionality(vector_manager: VectorStoreManager): """Test vector search with different roles and queries"""
    print("\n" + "="*70)
    print("🧪 TESTING VECTOR SEARCH FUNCTIONALITY")
    print("="*70)
    
    # Test queries for different roles
    test_scenarios = [
    {
        "role": UserRoles.FINANCE_TEAM,
        "queries": [
            "budget analysis",
            "revenue performance",
            "financial metrics"
        ]
    },
    {
        "role": UserRoles.MARKETING_TEAM,
        "queries": [
            "campaign performance",
            "customer acquisition",
            "marketing ROI"
        ]
    },
    {
        "role": UserRoles.HR_TEAM,
        "queries": [
            "employee policies",
            "vacation policy",
            "performance review"
        ]
    },
    {
        "role": UserRoles.ENGINEERING_TEAM,
        "queries": [
            "technical architecture",
            "development process",
            "code standards"
        ]
    },
    {
        "role": UserRoles.C_LEVEL_EXECUTIVES,
        "queries": [
            "strategic overview",
            "company performance",
            "quarterly results"
        ]
    },
    {
        "role": UserRoles.GENERAL_EMPLOYEES,
        "queries": [
            "company handbook",
            "office policies",
            "general information"
        ]
    }
]
    
    for scenario in test_scenarios:
        role = scenario[
    "role"
]
        print(f"\n👤 Testing {role.value.upper()}:")
        print("-" * 50)
        
        for query in scenario[
    "queries"
]:
            print(f"\n🔍 Query: '{query}'")
            
            start_time = time.time()
            results = await vector_manager.search(query, role, limit=3)
            search_time = time.time() - start_time
            
            print(f"⏱️  Search time: {search_time:.3f}s")
            print(f"📊 Results found: {len(results)}")
            
            if results:
                for i, result in enumerate(results,
1):
                    print(f"  {i}. {result.filename} (Score: {result.score:.3f}, Dept: {result.department})")
                    print(f"     Preview: {result.content[:100]}...")
            else:
                print("  ❌ No accessible results found")
            
            # Small delay between queries
            await asyncio.sleep(0.5)

async def test_autogen_integration(vector_manager: VectorStoreManager): """Test AutoGen tool integration"""
    print("\n" + "="*70)
    print("🤖 TESTING AUTOGEN INTEGRATION")
    print("="*70)
    
    # Test AutoGen search tools for different roles
    test_roles = [UserRoles.FINANCE_TEAM, UserRoles.MARKETING_TEAM
]
    
    for role in test_roles:
        print(f"\n👤 Testing AutoGen tools for {role.value}:")
        print("-" * 40)
        
        # Create search tool
        search_tool = vector_manager.create_autogen_search_tool(role)
        
        # Test the tool
        test_query = "performance metrics"
        print(f"🔧 Testing search tool with query: '{test_query}'")
        
        result = await search_tool(test_query, max_results=2)
        print(f"📋 Tool result:")
        print(result[
    : 500
] + "..." if len(result) > 500 else result)

def validate_vector_store_integrity(vector_manager: VectorStoreManager): """Validate vector store setup and integrity"""
    print("\n" + "="*70)
    print("🔍 VALIDATING VECTOR STORE INTEGRITY")
    print("="*70)
    
    checks = []
    
    # Check vector store connection
    if hasattr(vector_manager.vector_store, 'client') and vector_manager.vector_store.client:
        checks.append("✅ Vector store client connection")
    else:
        checks.append("❌ Vector store client connection")
    
    # Check collection exists
    if hasattr(vector_manager.vector_store, 'collection') and vector_manager.vector_store.collection:
        checks.append("✅ Collection exists")
    else:
        checks.append("❌ Collection exists")
    
    # Check embedding configuration
    if vector_manager.embedding_manager.config:
        checks.append(f"✅ Embedding model: {vector_manager.embedding_manager.embedding_model}")
        checks.append(f"✅ Vector dimensions: {vector_manager.embedding_manager.config['dimensions']}")
    else:
        checks.append("❌ Embedding configuration")
    
    print("\n📋 Integrity Check Results:")
    for check in checks:
        print(f"  {check}")
    
    return all("✅" in check for check in checks)

# ===============================
# 6. MAIN EXECUTION & SETUP
# ===============================

async def main(): """Main execution function for vector store setup"""
    print("🚀 Starting Vector Store Setup Pipeline...")
    
    # Import processed documents from Notebook 02
    print("\n📚 Loading processed documents from Notebook 02...")
    
    # Note: In a real implementation, you would load these from the previous notebook
    # For now, we'll simulate this
    try:
        # This would typically load from Notebook 02's output
        print("⚠️  Please ensure you've run Notebook 02 first to generate processed documents")
        print("💡 For testing, we'll create sample documents")
        
        # Create sample documents for testing
        sample_documents = create_sample_documents()
        print(f"📄 Loaded {len(sample_documents)} sample documents for testing")
        
    except Exception as e:
        print(f"❌ Error loading documents: {str(e)}")
        return None, None
    
    # Initialize ModelClient (from Notebook 01)
    model_client = None
    if ModelClient:
        try:
            model_client = ModelClient()
            print("✅ ModelClient initialized successfully")
        except Exception as e:
            print(f"⚠️  Could not initialize ModelClient: {str(e)}")
    
    # Initialize Embedding Manager
    print("\n🧠 Initializing Embedding Manager...")
    embedding_manager = EmbeddingManager(
        model_client=model_client,
        embedding_model="openai_3_large"  # Can be changed based on requirements
    )
    
    # Initialize Vector Store Manager
    print("\n🗄️  Initializing Vector Store Manager...")
    
    # Try Chroma first (easier to set up), fallback to Qdrant
    vector_backend = "chroma"
    try:
        vector_manager = VectorStoreManager(embedding_manager, backend=vector_backend)
    except Exception as e:
        print(f"⚠️  Error with {vector_backend}: {str(e)}")
        try:
            vector_backend = "qdrant"
            vector_manager = VectorStoreManager(embedding_manager, backend=vector_backend)
        except Exception as e2:
            print(f"❌ Error with both backends: {str(e2)}")
            return None, None
    
    # Setup and populate vector store
    print(f"\n📥 Setting up {vector_backend} vector store...")
    success = await vector_manager.setup_and_populate(sample_documents)
    
    if not success:
        print("❌ Vector store setup failed")
        return None, None
    
    # Validate setup
    print("\n🔍 Validating vector store setup...")
    integrity_ok = validate_vector_store_integrity(vector_manager)
    
    if not integrity_ok:
        print("⚠️  Some integrity checks failed")
    
    # Test functionality
    await test_vector_search_functionality(vector_manager)
    
    # Test AutoGen integration
    await test_autogen_integration(vector_manager)
    
    print("\n✅ Vector Store Setup completed successfully!")
    print("🎯 Ready for next phase: RBAC Logic Testing (Notebook 04)")
    
    return vector_manager, embedding_manager

def create_sample_documents() -> List[Document
]: """Create sample documents for testing (simulates Notebook 02 output)"""
    from langchain.schema import Document
    
    sample_docs = [
        Document(
            page_content="FinSolve Technologies achieved record revenue of $50M in Q4 2024, representing 25% growth over previous quarter. Operating expenses increased by 15% primarily due to increased headcount in engineering and sales teams.",
            metadata={
        "filename": "quarterly_financial_report.md",
        "department": "finance",
        "access_roles": [
            "finance_team",
            "c_level_executives"
        ],
        "sensitivity_level": "sensitive",
        "keywords": [
            "revenue",
            "growth",
            "expenses",
            "Q4",
            "financial"
        ],
        "chunk_index": 0,
        "total_chunks": 1,
        "document_hash": "abc123",
        "file_size": 1500
    }
        ),
        Document(
            page_content="Marketing campaign 'Digital First' launched in Q3 2024 resulted in 40% increase in customer acquisition. Cost per acquisition decreased from $150 to $120, improving overall marketing ROI to 3.2x.",
            metadata={
        "filename": "marketing_report_q3_2024.md",
        "department": "marketing",
        "access_roles": [
            "marketing_team",
            "finance_team",
            "c_level_executives"
        ],
        "sensitivity_level": "normal",
        "keywords": [
            "campaign",
            "acquisition",
            "ROI",
            "digital",
            "cost"
        ],
        "chunk_index": 0,
        "total_chunks": 1,
        "document_hash": "def456",
        "file_size": 1200
    }
        ),
        Document(
            page_content="Employee vacation policy allows for 15 days annual leave for employees with less than 2 years tenure, increasing to 20 days after 2 years and 25 days after 5 years. Vacation requests must be submitted at least 2 weeks in advance.",
            metadata={
        "filename": "employee_handbook.md",
        "department": "hr",
        "access_roles": [
            "hr_team",
            "c_level_executives",
            "general_employees"
        ],
        "sensitivity_level": "normal",
        "keywords": [
            "vacation",
            "leave",
            "policy",
            "employee",
            "tenure"
        ],
        "chunk_index": 0,
        "total_chunks": 1,
        "document_hash": "ghi789",
        "file_size": 800
    }
        ),
        Document(
            page_content="FinSolve technical architecture follows microservices pattern with Docker containerization. API gateway handles all external requests with rate limiting and authentication. Database layer uses PostgreSQL with Redis for caching.",
            metadata={
        "filename": "engineering_master_doc.md",
        "department": "engineering",
        "access_roles": [
            "engineering_team",
            "c_level_executives"
        ],
        "sensitivity_level": "sensitive",
        "keywords": [
            "architecture",
            "microservices",
            "docker",
            "API",
            "database"
        ],
        "chunk_index": 0,
        "total_chunks": 1,
        "document_hash": "jkl012",
        "file_size": 2000
    }
        ),
        Document(
            page_content="Company mission: To democratize financial services through innovative technology solutions. All employees are expected to uphold our core values of integrity, innovation, and customer focus in their daily work.",
            metadata={
        "filename": "company_handbook.md",
        "department": "general",
        "access_roles": [
            "finance_team",
            "marketing_team",
            "hr_team",
            "engineering_team",
            "c_level_executives",
            "general_employees"
        ],
        "sensitivity_level": "normal",
        "keywords": [
            "mission",
            "values",
            "integrity",
            "innovation",
            "customer"
        ],
        "chunk_index": 0,
        "total_chunks": 1,
        "document_hash": "mno345",
        "file_size": 600
    }
        )
]
    
    return sample_docs

# Run the main execution
if __name__ == "__main__":
    # Use asyncio for async main function
    vector_manager, embedding_manager = asyncio.run(main())

# ===============================
# 7. INTERACTIVE TESTING SECTION
# ===============================

print("\n" + "="*70)
print("🧪 INTERACTIVE TESTING SECTION")
print("="*70)

# Test vector search with different queries
if 'vector_manager' in locals() and vector_manager:
    print("\n🔬 Interactive Vector Search Testing:")
    
    # Example searches for different roles
    test_queries = [
        ("financial performance", UserRoles.FINANCE_TEAM),
        ("marketing campaigns", UserRoles.MARKETING_TEAM),
        ("employee policies", UserRoles.HR_TEAM),
        ("system architecture", UserRoles.ENGINEERING_TEAM),
        ("company overview", UserRoles.C_LEVEL_EXECUTIVES),
        ("general information", UserRoles.GENERAL_EMPLOYEES)
]
    
    async def run_interactive_tests():
        for query, role in test_queries:
            print(f"\n📝 Query: '{query}' | Role: {role.value}")
            results = await vector_manager.search(query, role, limit=2)
            
            if results:
                for i, result in enumerate(results,
1):
                    print(f"  {i}. {result.filename} (Score: {result.score:.3f})")
                    print(f"     {result.content[:120]}...")
            else:
                print("  ❌ No results found")
    
    # Run interactive tests
    asyncio.run(run_interactive_tests())

print("\n🎉 Notebook 03 completed successfully!")
print("📝 Next steps:")
print("  1. ✅ Model Client Testing (Notebook 01) - COMPLETED")
print("  2. ✅ Document Processing (Notebook 02) - COMPLETED")
print("  3. ✅ Vector Store Setup (Notebook 03) - COMPLETED")
print("  4. ⏳ RBAC Logic Testing (Notebook 04) - NEXT")
print("\n🚀 Vector Store Ready:")
print("  ✅ Embeddings generated and stored")
print("  ✅ RBAC-aware search functionality")
print("  ✅ AutoGen tool integration")
print("  ✅ Multi-backend support (Chroma/Qdrant)")
print("  🎯 Ready for agent creation and RBAC testing!")