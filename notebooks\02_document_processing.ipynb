# Document Processing for RAG-RBAC System
# FinSolve Technologies - AutoGen Multi-Agent Chatbot
# Notebook 02: Document Processing & RBAC Preparation

import os
import sys
from pathlib import Path
import json
import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
from datetime import datetime

# Document processing
import pandas as pd
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import DirectoryLoader, TextLoader
from langchain.schema import Document

# Data manipulation
import numpy as np

# AutoGen specific imports (using available version)
try:
    from autogen import ConversableAgent
    AUTOGEN_VERSION = "0.9.x"
except ImportError:
    try:
        from autogen_core.models import UserMessage
        AUTOGEN_VERSION = "0.6.x"
    except ImportError:
        AUTOGEN_VERSION = "none"
        print("⚠️ AutoGen not available - continuing with document processing only")

print("📚 FinSolve Technologies - Document Processing & RBAC Setup")
print("=" * 60)
print(f"🤖 AutoGen Version: {AUTOGEN_VERSION}")
print("=" * 60)

# ===============================
# 1. DOCUMENT STRUCTURE & RBAC CONFIGURATION
# ===============================

class UserRoles(Enum): """User roles for RBAC system (from project structure)"""
    FINANCE_TEAM = "finance_team"
    MARKETING_TEAM = "marketing_team"
    HR_TEAM = "hr_team"
    ENGINEERING_TEAM = "engineering_team"
    C_LEVEL_EXECUTIVES = "c_level_executives"
    GENERAL_EMPLOYEES = "general_employees"

@dataclass
class DocumentMetadata: """Enhanced document metadata for RAG-RBAC system with AutoGen 0.4 support"""
    filename: str
    department: str
    access_roles: List[str
]
    content_type: str
    file_size: int
    last_modified: str
    document_hash: str
    chunk_index: int = 0
    total_chunks: int = 1
    source_section: str = ""
    keywords: List[str
] = None
    sensitivity_level: str = "normal"  # normal, sensitive, confidential
    
    # AutoGen 0.4 specific fields
    agent_accessible: bool = True
    context_priority: int = 1  # 1=high,
2=medium,
3=low for AutoGen context ranking
    autogen_tools: List[str
] = None  # Tools that can access this document
    conversation_relevant: bool = True  # Whether to include in conversation context
    max_context_length: int = 2000  # Max chars for AutoGen context window

    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []
        if self.autogen_tools is None:
            self.autogen_tools = [
    "document_search",
    "context_assembly"
]
    
    def to_autogen_context(self) -> Dict: """Convert to AutoGen agent context format"""
        return {
    "source": self.filename,
    "department": self.department,
    "roles": self.access_roles,
    "sensitivity": self.sensitivity_level,
    "keywords": self.keywords,
    "chunk_info": f"{self.chunk_index + 1}/{self.total_chunks}",
    "priority": self.context_priority
}

# RBAC Configuration based on actual FinSolve documents
class RBACConfig: """Role-Based Access Control configuration"""
    
    # Available documents in the system
    AVAILABLE_DOCUMENTS = {
    "finance": [
        "financial_summary.md",
        "quarterly_financial_report.md"
    ],
    "marketing": [
        "marketing_report_2024.md",
        "marketing_report_q1_2024.md",
        "marketing_report_q2_2024.md",
        "marketing_report_q3_2024.md",
        "marketing_report_q4_2024.md"
    ],
    "hr": [
        "employee_handbook.md"
    ],
    "engineering": [
        "engineering_master_doc.md"
    ],
    "general": [
        "company_handbook.md",
        "problem_statement.md"
    ]
}
    
    # Document access permissions per role
    ROLE_DOCUMENT_ACCESS = {
        UserRoles.FINANCE_TEAM: [
            # Finance documents
            "financial_summary.md",
        "quarterly_financial_report.md",
            # Marketing expenses (from marketing reports)
            "marketing_report_2024.md",
        "marketing_report_q1_2024.md",
        "marketing_report_q2_2024.md",
        "marketing_report_q3_2024.md",
        "marketing_report_q4_2024.md",
            # General company info
            "company_handbook.md"
    ],
        UserRoles.MARKETING_TEAM: [
            # Marketing documents
            "marketing_report_2024.md",
        "marketing_report_q1_2024.md",
        "marketing_report_q2_2024.md",
        "marketing_report_q3_2024.md",
        "marketing_report_q4_2024.md",
            # General company info
            "company_handbook.md"
    ],
        UserRoles.HR_TEAM: [
            # HR documents
            "employee_handbook.md",
            # General company info
            "company_handbook.md"
    ],
        UserRoles.ENGINEERING_TEAM: [
            # Engineering documents
            "engineering_master_doc.md",
            # General company info
            "company_handbook.md"
    ],
        UserRoles.C_LEVEL_EXECUTIVES: [
            # All documents - full access
            "financial_summary.md",
        "quarterly_financial_report.md",
        "marketing_report_2024.md",
        "marketing_report_q1_2024.md",
        "marketing_report_q2_2024.md",
        "marketing_report_q3_2024.md",
        "marketing_report_q4_2024.md",
        "employee_handbook.md",
        "engineering_master_doc.md",
        "company_handbook.md",
        "problem_statement.md"
    ],
        UserRoles.GENERAL_EMPLOYEES: [
            # Only general company information
            "company_handbook.md"
    ]
}

# ===============================
# 2. DOCUMENT PROCESSING UTILITIES
# ===============================

class DocumentProcessor: """Enhanced document processor with RBAC integration"""
    
    def __init__(self, data_directory: str = "data/documents"):
        self.data_directory = Path(data_directory)
        self.rbac_config = RBACConfig()
        
        # Text splitter configuration optimized for AutoGen 0.4
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1500,  # Optimized for AutoGen context windows
            chunk_overlap=300,  # Increased overlap for better context continuity
            length_function=len,
            separators=[
    "\n\n",
    "\n",
    ". ",
    " ",
    ""
],
            keep_separator=True  # Maintain structure for AutoGen parsing
        )
        
        print(f"📁 Document processor initialized")
        print(f"📂 Data directory: {self.data_directory}")
    
    def get_document_department(self, filename: str) -> str: """Determine document department based on filename"""
        for dept, docs in self.rbac_config.AVAILABLE_DOCUMENTS.items():
            if filename in docs:
                return dept
        return "unknown"
    
    def get_document_access_roles(self, filename: str) -> List[str
]: """Get list of roles that can access this document"""
        access_roles = []
        for role, accessible_docs in self.rbac_config.ROLE_DOCUMENT_ACCESS.items():
            if filename in accessible_docs:
                access_roles.append(role.value)
        return access_roles
    
    def calculate_file_hash(self, content: str) -> str: """Calculate MD5 hash of document content"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def extract_keywords(self, content: str, max_keywords: int = 10) -> List[str
]: """Extract keywords from document content (simple implementation)"""
        # Remove common stop words and extract meaningful terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
}
        
        # Simple keyword extraction (can be enhanced with NLP libraries)
        words = re.findall(r'\b[a-zA-Z
]{
    4,
}\b', content.lower())
        word_freq = {}
        for word in words:
            if word not in stop_words:
                word_freq[word
] = word_freq.get(word,
0) + 1
        
        # Return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[
    1
], reverse=True)
        return [word for word, freq in sorted_words[
        :max_keywords
    ]
]
    
    def determine_sensitivity_level(self, content: str, department: str) -> str: """Determine document sensitivity level based on content and department"""
        content_lower = content.lower()
        
        # Keywords that indicate high sensitivity
        sensitive_keywords = ['confidential', 'salary', 'compensation', 'revenue', 'profit', 'loss', 'financial', 'budget', 'cost', 'expense', 'strategic', 'competition', 'competitive'
]
        confidential_keywords = ['performance review', 'disciplinary', 'termination', 'acquisition', 'merger', 'layoff', 'proprietary', 'trade secret'
]
        
        if any(keyword in content_lower for keyword in confidential_keywords):
            return "confidential"
        elif any(keyword in content_lower for keyword in sensitive_keywords):
            return "sensitive"
        elif department in ['finance', 'hr'
]:
            return "sensitive"
        else:
            return "normal"
    
    def load_document(self, file_path: Path) -> Optional[Document
]: """Load a single document with enhanced metadata"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            filename = file_path.name
            department = self.get_document_department(filename)
            access_roles = self.get_document_access_roles(filename)
            
            # Create enhanced metadata
            metadata = DocumentMetadata(
                filename=filename,
                department=department,
                access_roles=access_roles,
                content_type="markdown",
                file_size=len(content),
                last_modified=datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                document_hash=self.calculate_file_hash(content),
                keywords=self.extract_keywords(content),
                sensitivity_level=self.determine_sensitivity_level(content, department)
            )
            
            # Create LangChain document
            document = Document(
                page_content=content,
                metadata=asdict(metadata)
            )
            
            return document
            
        except Exception as e:
            print(f"❌ Error loading {file_path}: {str(e)}")
            return None
    
    def load_all_documents(self) -> List[Document
]: """Load all documents from the data directory"""
        documents = []
        
        print(f"🔍 Scanning for documents in {self.data_directory}")
        
        # Scan all subdirectories
        for dept_dir in self.data_directory.iterdir():
            if dept_dir.is_dir():
                print(f"📂 Processing {dept_dir.name} department...")
                
                for file_path in dept_dir.glob("*.md"):
                    print(f"  📄 Loading {file_path.name}...")
                    document = self.load_document(file_path)
                    if document:
                        documents.append(document)
                        print(f"    ✅ Loaded: {len(document.page_content)} characters")
                    else:
                        print(f"    ❌ Failed to load {file_path.name}")
        
        print(f"\n📊 Summary: Loaded {len(documents)} documents")
        return documents
    
    def chunk_documents(self, documents: List[Document
]) -> List[Document
]: """Split documents into chunks with preserved metadata"""
        chunked_documents = []
        
        print(f"✂️ Chunking {len(documents)} documents...")
        
        for doc in documents:
            # Split the document
            chunks = self.text_splitter.split_text(doc.page_content)
            
            # Create chunk documents with enhanced metadata
            for i, chunk in enumerate(chunks):
                # Copy original metadata
                chunk_metadata = doc.metadata.copy()
                
                # Update chunk-specific metadata
                chunk_metadata['chunk_index'
] = i
                chunk_metadata['total_chunks'
] = len(chunks)
                chunk_metadata['source_section'
] = f"chunk_{i}_of_{len(chunks)}"
                
                # Create new document for chunk
                chunk_doc = Document(
                    page_content=chunk,
                    metadata=chunk_metadata
                )
                
                chunked_documents.append(chunk_doc)
            
            print(f"  📄 {doc.metadata['filename']}: {len(chunks)} chunks")
        
        print(f"📊 Total chunks created: {len(chunked_documents)}")
        return chunked_documents
    
    def filter_documents_by_role(self, documents: List[Document
], user_role: UserRoles) -> List[Document
]: """Filter documents based on user role"""
        filtered_docs = []
        
        for doc in documents:
            if user_role.value in doc.metadata.get('access_roles',
[]):
                filtered_docs.append(doc)
        
        print(f"🔒 Role {user_role.value}: {len(filtered_docs)}/{len(documents)} documents accessible")
        return filtered_docs
    
    def prepare_documents_for_autogen(self, documents: List[Document
]) -> List[Dict
]: """Prepare documents in AutoGen 0.4 agent-ready format"""
        autogen_documents = []
        
        for doc in documents:
            # Create AutoGen-compatible document format
            autogen_doc = {
    "content": doc.page_content,
    "metadata": doc.metadata,
    "autogen_context": doc.metadata.get("autogen_context",
    {}),
    "search_content": self._create_search_content(doc),
    "agent_instructions": self._create_agent_instructions(doc)
}
            autogen_documents.append(autogen_doc)
        
        return autogen_documents
    
    def _create_search_content(self, doc: Document) -> str: """Create optimized content for vector search"""
        metadata = doc.metadata
        
        # Combine content with metadata for better search
        search_parts = [
            f"Department: {metadata.get('department', 'unknown')}",
            f"Document: {metadata.get('filename', 'unknown')}",
            f"Keywords: {', '.join(metadata.get('keywords', []))}",
            doc.page_content
]
        
        return " | ".join(search_parts)
    
    def _create_agent_instructions(self, doc: Document) -> Dict: """Create AutoGen agent-specific instructions for this document"""
        metadata = doc.metadata
        department = metadata.get('department', 'unknown')
        sensitivity = metadata.get('sensitivity_level', 'normal')
        
        instructions = {
    "context_usage": "Use this document to answer questions about " + department + " operations",
    "security_note": f"This is {sensitivity} information - respond accordingly",
    "source_attribution": f"Source: {metadata.get('filename', 'unknown')}",
    "department_focus": department
}
        
        # Department-specific instructions
        if department == "finance":
            instructions[
    "specific_guidance"
] = "Focus on financial metrics, budgets, and monetary values"
        elif department == "marketing":
            instructions[
    "specific_guidance"
] = "Focus on campaigns, customer insights, and market performance"
        elif department == "hr":
            instructions[
    "specific_guidance"
] = "Focus on policies, procedures, and employee guidelines"
        elif department == "engineering":
            instructions[
    "specific_guidance"
] = "Focus on technical architecture and development processes"
        else:
            instructions[
    "specific_guidance"
] = "Provide general company information"
        
        return instructions
    
    def create_autogen_context_summary(self, documents: List[Document
], max_context_length: int = 4000) -> str: """Create a condensed context summary for AutoGen agents"""
        context_parts = []
        current_length = 0
        
        # Sort by priority (if available in metadata)
        sorted_docs = sorted(documents, key=lambda d: d.metadata.get('context_priority',
2))
        
        for doc in sorted_docs:
            doc_summary = f"[{doc.metadata['department'].upper()}] {doc.metadata['filename']}: "
            
            # Add key content snippet
            content_snippet = doc.page_content[
    : 200
] + "..." if len(doc.page_content) > 200 else doc.page_content
            doc_summary += content_snippet
            
            if current_length + len(doc_summary) > max_context_length:
                break
                
            context_parts.append(doc_summary)
            current_length += len(doc_summary)
        
        return "\n\n".join(context_parts)

# ===============================
# 3. DOCUMENT ANALYSIS & STATISTICS
# ===============================

class DocumentAnalyzer: """Analyze document corpus for insights"""
    
    def __init__(self, documents: List[Document
]):
        self.documents = documents
    
    def generate_corpus_statistics(self) -> Dict: """Generate comprehensive statistics about the document corpus"""
        stats = {
            'total_documents': len(self.documents),
            'total_chunks': sum(1 for doc in self.documents if 'chunk_index' in doc.metadata),
            'departments': {},
            'access_roles': {},
            'sensitivity_levels': {},
            'content_types': {},
            'file_sizes': [],
            'keywords_frequency': {}
}
        
        # Analyze each document
        for doc in self.documents:
            metadata = doc.metadata
            
            # Department analysis
            dept = metadata.get('department', 'unknown')
            stats['departments'
][dept
] = stats['departments'
].get(dept,
0) + 1
            
            # Access roles analysis
            roles = metadata.get('access_roles',
[])
            for role in roles:
                stats['access_roles'
][role
] = stats['access_roles'
].get(role,
0) + 1
            
            # Sensitivity levels
            sensitivity = metadata.get('sensitivity_level', 'normal')
            stats['sensitivity_levels'
][sensitivity
] = stats['sensitivity_levels'
].get(sensitivity,
0) + 1
            
            # Content types
            content_type = metadata.get('content_type', 'unknown')
            stats['content_types'
][content_type
] = stats['content_types'
].get(content_type,
0) + 1
            
            # File sizes
            file_size = metadata.get('file_size',
0)
            stats['file_sizes'
].append(file_size)
            
            # Keywords frequency
            keywords = metadata.get('keywords',
[])
            for keyword in keywords:
                stats['keywords_frequency'
][keyword
] = stats['keywords_frequency'
].get(keyword,
0) + 1
        
        # Calculate file size statistics
        if stats['file_sizes'
]:
            stats['avg_file_size'
] = np.mean(stats['file_sizes'
])
            stats['total_content_size'
] = sum(stats['file_sizes'
])
        else:
            stats['avg_file_size'
] = 0
            stats['total_content_size'
] = 0
        
        return stats
    
    def print_corpus_report(self): """Print a detailed report of the document corpus"""
        stats = self.generate_corpus_statistics()
        
        print("\n" + "="*60)
        print("📊 DOCUMENT CORPUS ANALYSIS REPORT")
        print("="*60)
        
        print(f"\n📚 OVERVIEW:")
        print(f"  Total Documents: {stats['total_documents']}")
        print(f"  Total Chunks: {stats['total_chunks']}")
        print(f"  Total Content Size: {stats['total_content_size']:,} characters")
        print(f"  Average File Size: {stats['avg_file_size']:.0f} characters")
        
        print(f"\n📂 DEPARTMENTS:")
        for dept, count in sorted(stats['departments'
].items()):
            print(f"  {dept.title()}: {count} documents")
        
        print(f"\n👥 ACCESS ROLES:")
        for role, count in sorted(stats['access_roles'
].items()):
            print(f"  {role}: {count} documents")
        
        print(f"\n🔒 SENSITIVITY LEVELS:")
        for level, count in sorted(stats['sensitivity_levels'
].items()):
            print(f"  {level.title()}: {count} documents")
        
        print(f"\n🏷️ TOP KEYWORDS:")
        top_keywords = sorted(stats['keywords_frequency'
].items(), key=lambda x: x[
    1
], reverse=True)[
    : 10
]
        for keyword, freq in top_keywords:
            print(f"  {keyword}: {freq} occurrences")

# ===============================
# 4. AUTOGEN 0.4 INTEGRATION HELPERS
# ===============================

class AutoGenDocumentIntegrator: """Helper class for integrating processed documents with AutoGen 0.4 agents"""
    
    def __init__(self, documents: List[Document
]):
        self.documents = documents
        self.autogen_documents = None
    
    def prepare_for_agents(self) -> List[Dict
]: """Prepare all documents for AutoGen agent consumption"""
        processor = DocumentProcessor()
        self.autogen_documents = processor.prepare_documents_for_autogen(self.documents)
        return self.autogen_documents
    
    def create_document_tool_function(self, user_role: UserRoles): """Create a tool function for AutoGen agents to search documents"""
        
        def search_documents(query: str, max_results: int = 5) -> str: """
            Search accessible documents based on user query and role.
            
            Args:
                query: Search query string
                max_results: Maximum number of results to return
            
            Returns:
                Formatted search results with source attribution
            """
            # Filter documents by role
            processor = DocumentProcessor()
            accessible_docs = processor.filter_documents_by_role(self.documents, user_role)
            
            # Simple keyword-based search (will be enhanced with vector search in next notebook)
            results = []
            query_lower = query.lower()
            
            for doc in accessible_docs:
                content_lower = doc.page_content.lower()
                keywords = doc.metadata.get('keywords',
[])
                
                # Calculate relevance score
                score = 0
                if query_lower in content_lower:
                    score += 10
                
                for keyword in keywords:
                    if keyword.lower() in query_lower:
                        score += 5
                
                if score > 0:
                    results.append((score, doc))
            
            # Sort by relevance and limit results
            results.sort(key=lambda x: x[
    0
], reverse=True)
            top_results = results[
    :max_results
]
            
            if not top_results:
                return f"No documents found for query: '{query}' (Role: {user_role.value})"
            
            # Format results for AutoGen agent
            formatted_results = []
            for score, doc in top_results:
                result = f"**Source:** {doc.metadata['filename']} ({doc.metadata['department']})\n"
                result += f"**Content:** {doc.page_content[:300]}...\n"
                result += f"**Relevance Score:** {score}\n"
                formatted_results.append(result)
            
            return "\n---\n".join(formatted_results)
        
        return search_documents
    
    def create_context_assembly_function(self, user_role: UserRoles): """Create a context assembly function for AutoGen agents"""
        
        def assemble_context(topic: str, max_context_length: int = 3000) -> str: """
            Assemble relevant context for a specific topic based on user role.
            
            Args:
                topic: Topic or domain to focus context on
                max_context_length: Maximum context length in characters
            
            Returns:
                Assembled context string optimized for AutoGen conversation
            """
            # Filter documents by role
            processor = DocumentProcessor()
            accessible_docs = processor.filter_documents_by_role(self.documents, user_role)
            
            # Filter by topic relevance
            topic_lower = topic.lower()
            relevant_docs = []
            
            for doc in accessible_docs:
                content_lower = doc.page_content.lower()
                department = doc.metadata.get('department', '')
                keywords = doc.metadata.get('keywords',
[])
                
                # Check relevance
                if (topic_lower in content_lower or 
                    topic_lower in department.lower() or
                    any(topic_lower in keyword.lower() for keyword in keywords)):
                    relevant_docs.append(doc)
            
            if not relevant_docs:
                return f"No relevant context found for topic: '{topic}' (Role: {user_role.value})"
            
            # Create condensed context
            context = processor.create_autogen_context_summary(relevant_docs, max_context_length)
            
            return f"**Context for topic '{topic}' (Role: {user_role.value}):**\n\n{context}"
        
        return assemble_context
    
    def test_autogen_integration(self, user_role: UserRoles): """Test AutoGen integration with sample queries"""
        print(f"\n🤖 Testing AutoGen integration for {user_role.value}")
        print("-" * 50)
        
        # Create tool functions
        search_func = self.create_document_tool_function(user_role)
        context_func = self.create_context_assembly_function(user_role)
        
        # Test queries based on role
        test_queries = {
            UserRoles.FINANCE_TEAM: [
        "budget",
        "revenue",
        "financial performance"
    ],
            UserRoles.MARKETING_TEAM: [
        "campaign",
        "customer acquisition",
        "marketing metrics"
    ],
            UserRoles.HR_TEAM: [
        "employee policies",
        "handbook",
        "procedures"
    ],
            UserRoles.ENGINEERING_TEAM: [
        "architecture",
        "development",
        "technical"
    ],
            UserRoles.C_LEVEL_EXECUTIVES: [
        "strategy",
        "performance",
        "overview"
    ],
            UserRoles.GENERAL_EMPLOYEES: [
        "company policies",
        "general information"
    ]
}
        
        queries = test_queries.get(user_role,
[
    "general information"
])
        
        for query in queries[
    : 2
]:  # Test first 2 queries
            print(f"\n📝 Query: '{query}'")
            print("🔍 Search Results:")
            search_result = search_func(query, max_results=2)
            print(search_result[
    : 500
] + "..." if len(search_result) > 500 else search_result)
            
            print(f"\n🧠 Context Assembly:")
            context_result = context_func(query, max_context_length=1000)
            print(context_result[
    : 500
] + "..." if len(context_result) > 500 else context_result)
            print("\n" + "-" * 30)

# ===============================
# 5. TESTING & VALIDATION
# ===============================

def test_rbac_access_control(processor: DocumentProcessor, documents: List[Document
]): """Test RBAC access control for all roles"""
    print("\n" + "="*60)
    print("🔐 TESTING RBAC ACCESS CONTROL")
    print("="*60)
    
    for role in UserRoles:
        print(f"\n👤 Testing access for {role.value}:")
        filtered_docs = processor.filter_documents_by_role(documents, role)
        
        print(f"  Accessible documents:")
        for doc in filtered_docs:
            dept = doc.metadata['department'
]
            filename = doc.metadata['filename'
]
            sensitivity = doc.metadata['sensitivity_level'
]
            print(f"    📄 {filename} ({dept}, {sensitivity})")
        
        if not filtered_docs:
            print(f"    ❌ No documents accessible")

def test_document_integrity(documents: List[Document
]): """Test document integrity and metadata consistency"""
    print("\n" + "="*60)
    print("🔍 TESTING DOCUMENT INTEGRITY")
    print("="*60)
    
    issues = []
    
    for i, doc in enumerate(documents):
        metadata = doc.metadata
        
        # Check required metadata fields
        required_fields = ['filename', 'department', 'access_roles', 'document_hash'
]
        for field in required_fields:
            if field not in metadata or not metadata[field
]:
                issues.append(f"Document {i}: Missing or empty {field}")
        
        # Check content length
        if len(doc.page_content) < 50:
            issues.append(f"Document {i} ({metadata.get('filename', 'unknown')}): Content too short")
        
        # Check access roles validity
        access_roles = metadata.get('access_roles',
[])
        valid_roles = [role.value for role in UserRoles
]
        for role in access_roles:
            if role not in valid_roles:
                issues.append(f"Document {i}: Invalid access role '{role}'")
    
    if issues:
        print(f"⚠️  Found {len(issues)} issues:")
        for issue in issues[
    : 10
]:  # Show first 10 issues
            print(f"  • {issue}")
        if len(issues) > 10:
            print(f"  ... and {len(issues) - 10} more issues")
    else:
        print("✅ All documents passed integrity checks")

# ===============================
# 5. MAIN EXECUTION & TESTING
# ===============================

def main(): """Main execution function"""
    print("🚀 Starting Document Processing Pipeline...")
    
    # Setup data directory (you may need to adjust this path)
    data_dir = "data/documents"
    
    # Check if data directory exists
    if not Path(data_dir).exists():
        print(f"❌ Data directory not found: {data_dir}")
        print("📁 Please ensure your FinSolve documents are organized as:")
        print("   data/documents/")
        print("   ├── finance/")
        print("   │   ├── financial_summary.md")
        print("   │   └── quarterly_financial_report.md")
        print("   ├── marketing/")
        print("   │   ├── marketing_report_2024.md")
        print("   │   └── ...")
        print("   ├── hr/")
        print("   │   └── employee_handbook.md")
        print("   ├── engineering/")
        print("   │   └── engineering_master_doc.md")
        print("   └── general/")
        print("       ├── company_handbook.md")
        print("       └── problem_statement.md")
        return None, None, None
    
    # Initialize processor
    processor = DocumentProcessor(data_dir)
    
    # Load all documents
    print("\n" + "="*60)
    print("📚 LOADING DOCUMENTS")
    print("="*60)
    documents = processor.load_all_documents()
    
    if not documents:
        print("❌ No documents loaded. Please check your data directory.")
        return None, None, None
    
    # Create document analyzer
    analyzer = DocumentAnalyzer(documents)
    analyzer.print_corpus_report()
    
    # Test RBAC access control
    test_rbac_access_control(processor, documents)
    
    # Test document integrity
    test_document_integrity(documents)
    
    # Chunk documents for RAG
    print("\n" + "="*60)
    print("✂️ CHUNKING DOCUMENTS FOR RAG")
    print("="*60)
    chunked_documents = processor.chunk_documents(documents)
    
    # Analyze chunked documents
    chunk_analyzer = DocumentAnalyzer(chunked_documents)
    chunk_stats = chunk_analyzer.generate_corpus_statistics()
    
    print(f"\n📊 CHUNKING SUMMARY:")
    print(f"  Original documents: {len(documents)}")
    print(f"  Total chunks: {len(chunked_documents)}")
    print(f"  Average chunks per document: {len(chunked_documents) / len(documents):.1f}")
    
    # Test AutoGen 0.4 integration
    print("\n" + "="*60)
    print("🤖 TESTING AUTOGEN 0.4 INTEGRATION")
    print("="*60)
    integrator = AutoGenDocumentIntegrator(chunked_documents)
    autogen_docs = integrator.prepare_for_agents()
    
    print(f"✅ Prepared {len(autogen_docs)} documents for AutoGen agents")
    print("🔧 Created agent tool functions for document search and context assembly")
    
    # Test with sample role
    print("\n🧪 Testing AutoGen integration with Finance Team role:")
    integrator.test_autogen_integration(UserRoles.FINANCE_TEAM)
    
    print("\n✅ Document processing completed successfully!")
    print("🎯 Ready for next phase: Vector Store Setup (Notebook 03)")
    
    return documents, chunked_documents, processor, integrator

# Run the main execution
if __name__ == "__main__":
    original_documents, chunked_documents, document_processor, autogen_integrator = main()

# ===============================
# 7. INTERACTIVE TESTING SECTION
# ===============================

print("\n" + "="*60)
print("🧪 INTERACTIVE TESTING SECTION")
print("="*60)

# Example: Test role-based filtering
if original_documents and document_processor:
    print("\n🔬 Testing role-based document filtering:")
    
    # Test different roles
    test_roles = [UserRoles.FINANCE_TEAM, UserRoles.MARKETING_TEAM, UserRoles.GENERAL_EMPLOYEES
]
    
    for role in test_roles:
        print(f"\n👤 {role.value.upper()}:")
        filtered_docs = document_processor.filter_documents_by_role(original_documents, role)
        for doc in filtered_docs:
            print(f"  ✅ {doc.metadata['filename']} ({doc.metadata['department']})")

# Example: Show document metadata structure
if original_documents:
    print(f"\n📋 SAMPLE DOCUMENT METADATA:")
    sample_doc = original_documents[
    0
]
    print(f"Document: {sample_doc.metadata['filename']}")
    print(f"Content preview: {sample_doc.page_content[:200]}...")
    print(f"Metadata:")
    for key, value in sample_doc.metadata.items():
        if key != 'keywords' or len(value) <= 5:
            print(f"  {key}: {value}")
        else:
            print(f"  {key}: {value[:5]}... ({len(value)} total)")

# AutoGen 0.4 specific testing
if autogen_integrator:
    print(f"\n🤖 AUTOGEN 0.4 INTEGRATION TESTING:")
    
    # Test document preparation for AutoGen
    autogen_docs = autogen_integrator.prepare_for_agents()
    print(f"✅ Prepared {len(autogen_docs)} documents for AutoGen agents")
    
    # Show AutoGen document format
    if autogen_docs:
        sample_autogen_doc = autogen_docs[
    0
]
        print(f"\n📋 SAMPLE AUTOGEN DOCUMENT FORMAT:")
        print(f"Content length: {len(sample_autogen_doc['content'])} chars")
        print(f"Metadata keys: {list(sample_autogen_doc['metadata'].keys())}")
        print(f"Agent instructions: {sample_autogen_doc['agent_instructions']['context_usage']}")
    
    # Test tool functions for different roles
    print(f"\n🔧 TESTING AUTOGEN TOOL FUNCTIONS:")
    test_roles_autogen = [UserRoles.MARKETING_TEAM, UserRoles.HR_TEAM
]
    
    for role in test_roles_autogen:
        print(f"\n👤 Testing {role.value}:")
        
        # Test search function
        search_func = autogen_integrator.create_document_tool_function(role)
        search_result = search_func("performance", max_results=1)
        print(f"🔍 Search result preview: {search_result[:150]}...")
        
        # Test context assembly
        context_func = autogen_integrator.create_context_assembly_function(role)
        context_result = context_func("strategy", max_context_length=500)
        print(f"🧠 Context preview: {context_result[:150]}...")

print("\n🎉 Notebook 02 completed successfully!")
print("📝 Next steps:")
print("  1. ✅ Model Client Testing (Notebook 01) - COMPLETED")
print("  2. ✅ Document Processing (Notebook 02) - COMPLETED")
print("  3. ⏳ Vector Store Setup (Notebook 03) - NEXT")
print("\n🚀 AutoGen 0.4 Ready:")
print("  ✅ Documents formatted for AutoGen agents")
print("  ✅ Tool functions created for document search")
print("  ✅ Context assembly functions ready")
print("  ✅ RBAC integration tested")
print("  🎯 Ready for vector embeddings and AutoGen agent creation!")