# RBAC Logic Testing for AutoGen RAG System
# FinSolve Technologies - AutoGen Multi-Agent Chatbot
# Notebook 04: Role-Based Access Control Logic Testing with AutoGen 0.4

import os
import sys
from pathlib import Path
import json
import time
import asyncio
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime

# AutoGen 0.4 imports
from autogen import ConversableAgent, GroupChatManager, GroupChat
from autogen.coding import LocalCommandLineCodeExecutor

# Security and authentication
import jwt
import hashlib
from functools import wraps

# Import from previous notebooks
try:
    from model_client import ModelClient, ModelRouter  # From Notebook 01
    print("✅ Successfully imported ModelClient from Notebook 01")
except ImportError:
    print("⚠️  Could not import ModelClient - please ensure model_client.py is available")
    ModelClient = None

print("🔐 FinSolve Technologies - RBAC Logic Testing with AutoGen 0.4")
print("=" * 70)

# ===============================
# 1. RBAC SECURITY FRAMEWORK
# ===============================

class UserRoles(Enum):
    """User roles for RBAC system"""
    FINANCE_TEAM = "finance_team"
    MARKETING_TEAM = "marketing_team"
    HR_TEAM = "hr_team"
    ENGINEERING_TEAM = "engineering_team"
    C_LEVEL_EXECUTIVES = "c_level_executives"
    GENERAL_EMPLOYEES = "general_employees"

@dataclass
class UserSession:
    """User session with authentication and role information"""
    user_id: str
    username: str
    role: UserRoles
    department: str
    session_id: str
    created_at: datetime
    expires_at: datetime
    permissions: List[str]
    accessible_documents: List[str]
    query_count: int = 0
    max_queries: int = 100
    
    def is_valid(self) -> bool:
        """Check if session is still valid"""
        return datetime.now() < self.expires_at and self.query_count < self.max_queries
    
    def can_access_document(self, document_path: str) -> bool:
        """Check if user can access specific document"""
        return document_path in self.accessible_documents
    
    def increment_query_count(self):
        """Increment query count for rate limiting"""
        self.query_count += 1
    
    def to_context_string(self) -> str:
        """Convert to context string for AutoGen agents"""
        return f"User: {self.username} | Role: {self.role.value} | Dept: {self.department} | Queries: {self.query_count}/{self.max_queries}"

class RBACEngine:
    """Role-Based Access Control Engine"""
    
    def __init__(self):
        self.active_sessions: Dict[str, UserSession] = {}
        self.role_permissions = self._initialize_role_permissions()
        self.document_access_matrix = self._initialize_document_access()
        
        print("🔐 RBAC Engine initialized")
        print(f"👥 Configured roles: {len(UserRoles)}")
        print(f"📋 Permission matrix: {len(self.role_permissions)} roles")
    
    def _initialize_role_permissions(self) -> Dict[UserRoles, List[str]]:
        """Initialize role-based permissions"""
        return {
            UserRoles.FINANCE_TEAM: [
                "read_financial_reports",
                "read_marketing_expenses", 
                "read_budget_data",
                "read_company_policies"
            ],
            UserRoles.MARKETING_TEAM: [
                "read_marketing_reports",
                "read_campaign_data",
                "read_customer_metrics",
                "read_company_policies"
            ],
            UserRoles.HR_TEAM: [
                "read_employee_handbook",
                "read_hr_policies",
                "read_company_policies"
            ],
            UserRoles.ENGINEERING_TEAM: [
                "read_technical_docs",
                "read_architecture_specs",
                "read_development_guides",
                "read_company_policies"
            ],
            UserRoles.C_LEVEL_EXECUTIVES: [
                "read_all_reports",
                "read_strategic_data",
                "read_financial_reports",
                "read_marketing_reports",
                "read_hr_data",
                "read_technical_docs",
                "read_company_policies"
            ],
            UserRoles.GENERAL_EMPLOYEES: [
                "read_company_policies",
                "read_general_info"
            ]
        }
    
    def _initialize_document_access(self) -> Dict[UserRoles, List[str]]:
        """Initialize document access matrix (from project structure)"""
        return {
            UserRoles.FINANCE_TEAM: [
                "financial_summary.md",
                "quarterly_financial_report.md",
                "marketing_report_2024.md",
                "marketing_report_q1_2024.md",
                "marketing_report_q2_2024.md", 
                "marketing_report_q3_2024.md",
                "marketing_report_q4_2024.md",
                "company_handbook.md"
            ],
            UserRoles.MARKETING_TEAM: [
                "marketing_report_2024.md",
                "marketing_report_q1_2024.md",
                "marketing_report_q2_2024.md",
                "marketing_report_q3_2024.md", 
                "marketing_report_q4_2024.md",
                "company_handbook.md"
            ],
            UserRoles.HR_TEAM: [
                "employee_handbook.md",
                "company_handbook.md"
            ],
            UserRoles.ENGINEERING_TEAM: [
                "engineering_master_doc.md",
                "company_handbook.md"
            ],
            UserRoles.C_LEVEL_EXECUTIVES: [
                # Full access to all documents
                "financial_summary.md",
                "quarterly_financial_report.md",
                "marketing_report_2024.md",
                "marketing_report_q1_2024.md",
                "marketing_report_q2_2024.md",
                "marketing_report_q3_2024.md",
                "marketing_report_q4_2024.md",
                "employee_handbook.md",
                "engineering_master_doc.md",
                "company_handbook.md",
                "problem_statement.md"
            ],
            UserRoles.GENERAL_EMPLOYEES: [
                "company_handbook.md"
            ]
        }
    
    def create_user_session(self, user_id: str, username: str, role: UserRoles, department: str) -> UserSession:
        """Create a new authenticated user session"""
        session_id = hashlib.md5(f"{user_id}_{username}_{time.time()}".encode()).hexdigest()
        
        session = UserSession(
            user_id=user_id,
            username=username,
            role=role,
            department=department,
            session_id=session_id,
            created_at=datetime.now(),
            expires_at=datetime.now().replace(hour=23, minute=59, second=59),  # Expires end of day
            permissions=self.role_permissions.get(role, []),
            accessible_documents=self.document_access_matrix.get(role, []),
            max_queries=self._get_max_queries_for_role(role)
        )
        
        self.active_sessions[session_id] = session
        
        print(f"✅ Created session for {username} ({role.value})")
        print(f"   Session ID: {session_id[:8]}...")
        print(f"   Permissions: {len(session.permissions)}")
        print(f"   Accessible docs: {len(session.accessible_documents)}")
        
        return session
    
    def _get_max_queries_for_role(self, role: UserRoles) -> int:
        """Get maximum queries allowed per day for role"""
        query_limits = {
            UserRoles.C_LEVEL_EXECUTIVES: 500,
            UserRoles.ENGINEERING_TEAM: 150,
            UserRoles.FINANCE_TEAM: 100,
            UserRoles.MARKETING_TEAM: 80,
            UserRoles.HR_TEAM: 120,
            UserRoles.GENERAL_EMPLOYEES: 50
        }
        return query_limits.get(role, 50)
    
    def validate_session(self, session_id: str) -> Optional[UserSession]:
        """Validate and return user session"""
        session = self.active_sessions.get(session_id)
        
        if not session:
            print(f"❌ Session not found: {session_id}")
            return None
        
        if not session.is_valid():
            print(f"❌ Session expired or quota exceeded: {session_id}")
            return None
        
        return session
    
    def validate_document_access(self, session: UserSession, document_path: str) -> bool:
        """Validate if user can access specific document"""
        if not session.is_valid():
            return False
        
        return session.can_access_document(document_path)
    
    def get_role_statistics(self) -> Dict:
        """Get statistics about role usage"""
        stats = {
            "total_sessions": len(self.active_sessions),
            "role_distribution": {},
            "query_usage": {},
            "document_access_attempts": {}
        }
        
        for session in self.active_sessions.values():
            role = session.role.value
            stats["role_distribution"][role] = stats["role_distribution"].get(role, 0) + 1
            stats["query_usage"][role] = stats["query_usage"].get(role, 0) + session.query_count
        
        return stats

# ===============================
# 2. AUTOGEN 0.4 RBAC AGENTS
# ===============================

class RBACAgentFactory:
    """Factory for creating AutoGen 0.4 agents with RBAC capabilities"""
    
    def __init__(self, rbac_engine: RBACEngine, model_client: Optional[ModelClient] = None):
        self.rbac_engine = rbac_engine
        self.model_client = model_client
        
        # Agent configurations optimized for different roles
        self.agent_configs = {
            "security_agent": {
                "model": "gpt-4-mini",  # Fast for authentication
                "system_message": "You are a security agent responsible for authentication and authorization. Always verify user permissions before allowing access to documents.",
                "max_consecutive_auto_reply": 3
            },
            "document_agent": {
                "model": "gpt-4o",  # Smart for document understanding
                "system_message": "You are a document specialist. You help users find and access documents based on their role permissions.",
                "max_consecutive_auto_reply": 5
            },
            "role_specialist": {
                "model": "gpt-4o",  # Role-specific responses
                "system_message": "You are a role-specific specialist. Provide responses tailored to the user's department and role.",
                "max_consecutive_auto_reply": 5
            }
        }
        
        print("🤖 RBAC Agent Factory initialized")
        print(f"⚙️  Agent configurations: {len(self.agent_configs)}")
    
    def create_security_agent(self, session: UserSession) -> ConversableAgent:
        """Create security/authentication agent"""
        
        system_message = f"""You are the Security Agent for FinSolve Technologies.

Current User Session:
- User: {session.username}
- Role: {session.role.value}
- Department: {session.department}
- Permissions: {', '.join(session.permissions)}
- Query Count: {session.query_count}/{session.max_queries}

Your responsibilities:
1. Validate user permissions before document access
2. Enforce role-based access control
3. Monitor query limits
4. Log security events

Always respond with security context and validate access before proceeding."""
        
        # Create tools for the security agent
        def validate_access(document_name: str) -> str:
            """Validate if current user can access the specified document"""
            if self.rbac_engine.validate_document_access(session, document_name):
                return f"✅ ACCESS GRANTED: {session.username} can access {document_name}"
            else:
                return f"❌ ACCESS DENIED: {session.username} cannot access {document_name}"
        
        def check_query_limit() -> str:
            """Check current query usage against limits"""
            if session.query_count >= session.max_queries:
                return f"❌ QUOTA EXCEEDED: {session.query_count}/{session.max_queries} queries used"
            else:
                remaining = session.max_queries - session.query_count
                return f"✅ QUOTA OK: {remaining} queries remaining"
        
        agent = ConversableAgent(
            name=f"SecurityAgent_{session.role.value}",
            system_message=system_message,
            llm_config={
                "model": self.agent_configs["security_agent"]["model"],
                "temperature": 0.1,  # Low temperature for consistent security responses
            },
            max_consecutive_auto_reply=self.agent_configs["security_agent"]["max_consecutive_auto_reply"],
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="validate_access")(validate_access)
        agent.register_for_llm(name="validate_access", description="Validate document access permissions")(validate_access)
        
        agent.register_for_execution(name="check_query_limit")(check_query_limit)
        agent.register_for_llm(name="check_query_limit", description="Check query quota status")(check_query_limit)
        
        return agent
    
    def create_document_agent(self, session: UserSession, vector_manager=None) -> ConversableAgent:
        """Create document search and retrieval agent"""
        
        system_message = f"""You are the Document Agent for FinSolve Technologies.

Current User Session:
- User: {session.username}
- Role: {session.role.value}
- Department: {session.department}
- Accessible Documents: {len(session.accessible_documents)} documents

Your responsibilities:
1. Search and retrieve documents based on user queries
2. Respect role-based access controls
3. Provide relevant document summaries
4. Cite sources and maintain document traceability

Available documents for this user:
{chr(10).join([f"- {doc}" for doc in session.accessible_documents[:10]])}
{"..." if len(session.accessible_documents) > 10 else ""}

Always include source attribution and respect access permissions."""
        
        # Create tools for document agent
        def search_accessible_documents(query: str, max_results: int = 5) -> str:
            """Search documents accessible to current user"""
            session.increment_query_count()
            
            # Simple search simulation (would use vector_manager in real implementation)
            accessible_docs = session.accessible_documents
            query_lower = query.lower()
            
            # Mock search results based on keywords
            results = []
            for doc in accessible_docs:
                if any(keyword in doc.lower() for keyword in query_lower.split()):
                    results.append(doc)
            
            if not results:
                return f"No accessible documents found for query: '{query}'"
            
            results_text = f"Found {len(results)} accessible documents for '{query}':\n"
            for i, doc in enumerate(results[:max_results], 1):
                results_text += f"{i}. {doc}\n"
            
            return results_text
        
        def get_document_summary(document_name: str) -> str:
            """Get summary of specific document if accessible"""
            if not self.rbac_engine.validate_document_access(session, document_name):
                return f"❌ Access denied to {document_name}"
            
            session.increment_query_count()
            
            # Mock document summaries
            summaries = {
                "financial_summary.md": "Financial overview showing revenue growth and expense analysis",
                "marketing_report_q3_2024.md": "Q3 marketing performance with campaign metrics and ROI analysis",
                "employee_handbook.md": "Employee policies, procedures, and company guidelines",
                "engineering_master_doc.md": "Technical architecture, development processes, and system documentation",
                "company_handbook.md": "General company information, mission, and basic policies"
            }
            
            summary = summaries.get(document_name, "Document summary not available")
            return f"📄 {document_name}: {summary}"
        
        agent = ConversableAgent(
            name=f"DocumentAgent_{session.role.value}",
            system_message=system_message,
            llm_config={
                "model": self.agent_configs["document_agent"]["model"],
                "temperature": 0.3,
            },
            max_consecutive_auto_reply=self.agent_configs["document_agent"]["max_consecutive_auto_reply"],
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register tools
        agent.register_for_execution(name="search_accessible_documents")(search_accessible_documents)
        agent.register_for_llm(name="search_accessible_documents", description="Search documents accessible to current user")(search_accessible_documents)
        
        agent.register_for_execution(name="get_document_summary")(get_document_summary)
        agent.register_for_llm(name="get_document_summary", description="Get summary of specific document")(get_document_summary)
        
        return agent
    
    def create_role_specialist_agent(self, session: UserSession) -> ConversableAgent:
        """Create role-specific specialist agent"""
        
        # Role-specific system messages
        role_specializations = {
            UserRoles.FINANCE_TEAM: "You are a Finance Specialist. Focus on financial metrics, budgets, revenue, costs, and financial analysis. Provide insights relevant to financial decision-making.",
            UserRoles.MARKETING_TEAM: "You are a Marketing Specialist. Focus on campaigns, customer acquisition, market analysis, and marketing ROI. Provide insights for marketing strategy and performance.",
            UserRoles.HR_TEAM: "You are an HR Specialist. Focus on employee policies, procedures, performance management, and organizational guidelines. Provide guidance on HR matters.",
            UserRoles.ENGINEERING_TEAM: "You are a Technical Specialist. Focus on architecture, development processes, technical documentation, and engineering best practices.",
            UserRoles.C_LEVEL_EXECUTIVES: "You are an Executive Advisor. Provide high-level strategic insights, cross-functional analysis, and executive-level summaries across all business areas.",
            UserRoles.GENERAL_EMPLOYEES: "You are a General Information Assistant. Focus on company policies, general procedures, and basic information accessible to all employees."
        }
        
        specialization = role_specializations.get(session.role, "You are a general assistant.")
        
        system_message = f"""{specialization}

Current User Context:
- User: {session.username}
- Role: {session.role.value}
- Department: {session.department}

Tailor all responses to be relevant and appropriate for this user's role and responsibilities. Always maintain professional context appropriate for {session.role.value} level discussions."""
        
        # Role-specific tools
        def get_role_insights(topic: str) -> str:
            """Provide role-specific insights on a topic"""
            session.increment_query_count()
            
            role_perspectives = {
                UserRoles.FINANCE_TEAM: f"Financial perspective on {topic}: Focus on cost implications, ROI, budget impact, and financial metrics.",
                UserRoles.MARKETING_TEAM: f"Marketing perspective on {topic}: Consider customer impact, brand implications, market positioning, and campaign opportunities.",
                UserRoles.HR_TEAM: f"HR perspective on {topic}: Evaluate employee impact, policy implications, organizational change, and compliance considerations.",
                UserRoles.ENGINEERING_TEAM: f"Technical perspective on {topic}: Assess technical feasibility, architecture implications, development requirements, and system impact.",
                UserRoles.C_LEVEL_EXECUTIVES: f"Strategic perspective on {topic}: High-level analysis including business impact, competitive advantages, resource allocation, and long-term implications.",
                UserRoles.GENERAL_EMPLOYEES: f"General perspective on {topic}: Basic information and how it relates to daily operations and company policies."
            }
            
            return role_perspectives.get(session.role, f"General insights on {topic}")
        
        agent = ConversableAgent(
            name=f"SpecialistAgent_{session.role.value}",
            system_message=system_message,
            llm_config={
                "model": self.agent_configs["role_specialist"]["model"],
                "temperature": 0.5,
            },
            max_consecutive_auto_reply=self.agent_configs["role_specialist"]["max_consecutive_auto_reply"],
            human_input_mode="NEVER",
            code_execution_config=False
        )
        
        # Register role-specific tools
        agent.register_for_execution(name="get_role_insights")(get_role_insights)
        agent.register_for_llm(name="get_role_insights", description=f"Get {session.role.value} specific insights on a topic")(get_role_insights)
        
        return agent

# ===============================
# 3. RBAC TESTING FRAMEWORK
# ===============================

class RBACTestSuite:
    """Comprehensive RBAC testing framework"""
    
    def __init__(self, rbac_engine: RBACEngine, agent_factory: RBACAgentFactory):
        self.rbac_engine = rbac_engine
        self.agent_factory = agent_factory
        self.test_results = []
        
        print("🧪 RBAC Test Suite initialized")
    
    async def test_role_access_permissions(self) -> Dict:
        """Test document access permissions for all roles"""
        print("\n" + "="*70)
        print("🔐 TESTING ROLE ACCESS PERMISSIONS")
        print("="*70)
        
        test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "role_results": {}
        }
        
        # Test documents that should be accessible/inaccessible for each role
        test_scenarios = {
            UserRoles.FINANCE_TEAM: {
                "should_access": ["financial_summary.md", "marketing_report_q1_2024.md", "company_handbook.md"],
                "should_deny": ["employee_handbook.md", "engineering_master_doc.md"]
            },
            UserRoles.MARKETING_TEAM: {
                "should_access": ["marketing_report_2024.md", "company_handbook.md"],
                "should_deny": ["financial_summary.md", "employee_handbook.md", "engineering_master_doc.md"]
            },
            UserRoles.HR_TEAM: {
                "should_access": ["employee_handbook.md", "company_handbook.md"],
                "should_deny": ["financial_summary.md", "marketing_report_2024.md", "engineering_master_doc.md"]
            },
            UserRoles.ENGINEERING_TEAM: {
                "should_access": ["engineering_master_doc.md", "company_handbook.md"],
                "should_deny": ["financial_summary.md", "employee_handbook.md", "marketing_report_2024.md"]
            },
            UserRoles.C_LEVEL_EXECUTIVES: {
                "should_access": ["financial_summary.md", "marketing_report_2024.md", "employee_handbook.md", "engineering_master_doc.md"],
                "should_deny": []  # C-level should have access to everything
            },
            UserRoles.GENERAL_EMPLOYEES: {
                "should_access": ["company_handbook.md"],
                "should_deny": ["financial_summary.md", "marketing_report_2024.md", "employee_handbook.md", "engineering_master_doc.md"]
            }
        }
        
        for role, scenarios in test_scenarios.items():
            print(f"\n👤 Testing {role.value.upper()}:")
            print("-" * 50)
            
            # Create test session
            session = self.rbac_engine.create_user_session(
                user_id=f"test_{role.value}",
                username=f"Test User ({role.value})",
                role=role,
                department=role.value.split('_')[0]
            )
            
            role_results = {
                "role": role.value,
                "access_tests": [],
                "deny_tests": [],
                "passed": 0,
                "failed": 0
            }
            
            # Test documents that should be accessible
            for doc in scenarios["should_access"]:
                test_results["total_tests"] += 1
                
                can_access = self.rbac_engine.validate_document_access(session, doc)
                if can_access:
                    print(f"  ✅ PASS: Can access {doc}")
                    role_results["access_tests"].append({"document": doc, "result": "PASS"})
                    role_results["passed"] += 1
                    test_results["passed_tests"] += 1
                else:
                    print(f"  ❌ FAIL: Should access {doc} but cannot")
                    role_results["access_tests"].append({"document": doc, "result": "FAIL"})
                    role_results["failed"] += 1
                    test_results["failed_tests"] += 1
            
            # Test documents that should be denied
            for doc in scenarios["should_deny"]:
                test_results["total_tests"] += 1
                
                can_access = self.rbac_engine.validate_document_access(session, doc)
                if not can_access:
                    print(f"  ✅ PASS: Correctly denied access to {doc}")
                    role_results["deny_tests"].append({"document": doc, "result": "PASS"})
                    role_results["passed"] += 1
                    test_results["passed_tests"] += 1
                else:
                    print(f"  ❌ FAIL: Should deny {doc} but allowed access")
                    role_results["deny_tests"].append({"document": doc, "result": "FAIL"})
                    role_results["failed"] += 1
                    test_results["failed_tests"] += 1
            
            test_results["role_results"][role.value] = role_results
        
        return test_results
    
    async def test_autogen_agent_security(self) -> Dict:
        """Test AutoGen agents respect RBAC controls"""
        print("\n" + "="*70)
        print("🤖 TESTING AUTOGEN AGENT SECURITY")
        print("="*70)
        
        test_results = {
            "agent_tests": [],
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0
        }
        
        # Test different roles with AutoGen agents
        test_roles = [UserRoles.FINANCE_TEAM, UserRoles.MARKETING_TEAM, UserRoles.HR_TEAM, UserRoles.GENERAL_EMPLOYEES]
        
        for role in test_roles:
            print(f"\n👤 Testing AutoGen agents for {role.value.upper()}:")
            print("-" * 50)
            
            # Create session and agents
            session = self.rbac_engine.create_user_session(
                user_id=f"agent_test_{role.value}",
                username=f"Agent Test User ({role.value})",
                role=role,
                department=role.value.split('_')[0]
            )
            
            security_agent = self.agent_factory.create_security_agent(session)
            document_agent = self.agent_factory.create_document_agent(session)
            specialist_agent = self.agent_factory.create_role_specialist_agent(session)
            
            # Test security agent
            print("  🔒 Testing Security Agent...")
            
            # Test document access validation
            test_doc = "financial_summary.md"
            test_results["total_tests"] += 1
            
            try:
                response = security_agent.generate_reply(
                    messages=[{"role": "user", "content": f"Can I access {test_doc}?"}]
                )
                
                should_access = session.can_access_document(test_doc)
                response_grants_access = "ACCESS GRANTED" in str(response) or "✅" in str(response)
                
                if should_access == response_grants_access:
                    print(f"    ✅ PASS: Security agent correctly handled {test_doc}")
                    test_results["passed_tests"] += 1
                else:
                    print(f"    ❌ FAIL: Security agent incorrectly handled {test_doc}")
                    test_results["failed_tests"] += 1
            
            except Exception as e:
                print(f"    ❌ ERROR: Security agent test failed: {str(e)}")
                test_results["failed_tests"] += 1
            
            # Test document agent
            print("  📄 Testing Document Agent...")
            test_results["total_tests"] += 1
            
            try:
                response = document_agent.generate_reply(
                    messages=[{"role": "user", "content": "What documents can I access?"}]
                )
                
                # Check if response mentions appropriate documents
                accessible_docs = session.accessible_documents
                response_str = str(response)
                
                # Simple validation - should mention some accessible documents
                mentions_accessible = any(doc.replace('.md', '') in response_str for doc in accessible_docs[:3])
                
                if mentions_accessible:
                    print(f"    ✅ PASS: Document agent mentioned accessible documents")
                    test_results["passed_tests"] += 1
                else:
                    print(f"    ❌ FAIL: Document agent didn't mention accessible documents")
                    test_results["failed_tests"] += 1
            
            except Exception as e:
                print(f"    ❌ ERROR: Document agent test failed: {str(e)}")
                test_results["failed_tests"] += 1
            
            # Test specialist agent
            print("  🎯 Testing Specialist Agent...")
            test_results["total_tests"] += 1
            
            try:
                response = specialist_agent.generate_reply(
                    messages=[{"role": "user", "content": "Give me insights on company performance"}]
                )
                
                # Check if response is role-appropriate
                response_str = str(response).lower()
                role_keywords = {
                    UserRoles.FINANCE_TEAM: ["financial", "cost", "revenue", "budget"],
                    UserRoles.MARKETING_TEAM: ["marketing", "campaign", "customer", "market"],
                    UserRoles.HR_TEAM: ["employee", "policy", "organizational", "hr"],
                    UserRoles.GENERAL_EMPLOYEES: ["general", "company", "basic", "information"]
                }
                
                expected_keywords = role_keywords.get(role, [])
                has_role_context = any(keyword in response_str for keyword in expected_keywords)
                
                if has_role_context:
                    print(f"    ✅ PASS: Specialist agent provided role-appropriate response")
                    test_results["passed_tests"] += 1
                else:
                    print(f"    ❌ FAIL: Specialist agent response not role-appropriate")
                    test_results["failed_tests"] += 1
            
            except Exception as e:
                print(f"    ❌ ERROR: Specialist agent test failed: {str(e)}")
                test_results["failed_tests"] += 1
            
            # Store agent test results
            agent_result = {
                "role": role.value,
                "security_agent": "tested",
                "document_agent": "tested", 
                "specialist_agent": "tested",
                "session_id": session.session_id
            }
            test_results["agent_tests"].append(agent_result)
        
        return test_results
    
    async def test_query_rate_limiting(self) -> Dict:
        """Test query rate limiting and quota enforcement"""
        print("\n" + "="*70)
        print("⏱️  TESTING QUERY RATE LIMITING")
        print("="*70)
        
        test_results = {
            "rate_limit_tests": [],
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0
        }
        
        # Create a test session with low quota for testing
        test_session = self.rbac_engine.create_user_session(
            user_id="rate_limit_test",
            username="Rate Limit Test User",
            role=UserRoles.GENERAL_EMPLOYEES,
            department="test"
        )
        
        # Override quota for testing
        original_max = test_session.max_queries
        test_session.max_queries = 5  # Set low limit for testing
        
        print(f"🔄 Testing with quota limit: {test_session.max_queries}")
        
        # Create document agent for testing
        document_agent = self.agent_factory.create_document_agent(test_session)
        
        # Test query counting
        for i in range(test_session.max_queries + 2):  # Try to exceed limit
            test_results["total_tests"] += 1
            
            try:
                # Simulate query
                test_session.increment_query_count()
                
                is_within_limit = test_session.query_count <= test_session.max_queries
                session_valid = test_session.is_valid()
                
                if i < test_session.max_queries:
                    # Should be allowed
                    if session_valid:
                        print(f"  ✅ Query {i+1}: Allowed (within limit)")
                        test_results["passed_tests"] += 1
                    else:
                        print(f"  ❌ Query {i+1}: Should be allowed but blocked")
                        test_results["failed_tests"] += 1
                else:
                    # Should be blocked
                    if not session_valid:
                        print(f"  ✅ Query {i+1}: Correctly blocked (quota exceeded)")
                        test_results["passed_tests"] += 1
                    else:
                        print(f"  ❌ Query {i+1}: Should be blocked but allowed")
                        test_results["failed_tests"] += 1
            
            except Exception as e:
                print(f"  ❌ ERROR: Query {i+1} failed: {str(e)}")
                test_results["failed_tests"] += 1
        
        # Restore original quota
        test_session.max_queries = original_max
        
        test_results["rate_limit_tests"].append({
            "session_id": test_session.session_id,
            "final_query_count": test_session.query_count,
            "quota_limit": test_session.max_queries
        })
        
        return test_results
    
    async def test_session_management(self) -> Dict:
        """Test user session creation, validation, and expiration"""
        print("\n" + "="*70)
        print("👥 TESTING SESSION MANAGEMENT")
        print("="*70)
        
        test_results = {
            "session_tests": [],
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0
        }
        
        # Test session creation for all roles
        for role in UserRoles:
            test_results["total_tests"] += 1
            
            try:
                session = self.rbac_engine.create_user_session(
                    user_id=f"session_test_{role.value}",
                    username=f"Session Test ({role.value})",
                    role=role,
                    department=role.value.split('_')[0]
                )
                
                # Validate session was created properly
                if (session.role == role and 
                    session.session_id in self.rbac_engine.active_sessions and
                    session.is_valid()):
                    
                    print(f"  ✅ PASS: {role.value} session created successfully")
                    test_results["passed_tests"] += 1
                    
                    # Test session validation
                    test_results["total_tests"] += 1
                    validated_session = self.rbac_engine.validate_session(session.session_id)
                    
                    if validated_session and validated_session.session_id == session.session_id:
                        print(f"    ✅ PASS: Session validation works")
                        test_results["passed_tests"] += 1
                    else:
                        print(f"    ❌ FAIL: Session validation failed")
                        test_results["failed_tests"] += 1
                
                else:
                    print(f"  ❌ FAIL: {role.value} session creation failed")
                    test_results["failed_tests"] += 1
                
                test_results["session_tests"].append({
                    "role": role.value,
                    "session_id": session.session_id,
                    "permissions": len(session.permissions),
                    "accessible_docs": len(session.accessible_documents)
                })
            
            except Exception as e:
                print(f"  ❌ ERROR: {role.value} session test failed: {str(e)}")
                test_results["failed_tests"] += 1
        
        return test_results

# ===============================
# 4. INTEGRATION TESTING
# ===============================

async def test_full_rbac_workflow():
    """Test complete RBAC workflow with AutoGen agents"""
    print("\n" + "="*70)
    print("🔄 TESTING FULL RBAC WORKFLOW")
    print("="*70)
    
    # Initialize RBAC system
    rbac_engine = RBACEngine()
    
    # Initialize ModelClient if available
    model_client = None
    if ModelClient:
        try:
            model_client = ModelClient()
            print("✅ ModelClient initialized for agents")
        except Exception as e:
            print(f"⚠️  ModelClient not available: {str(e)}")
    
    # Initialize agent factory
    agent_factory = RBACAgentFactory(rbac_engine, model_client)
    
    # Create test scenario: Finance user asking about budget
    print("\n📋 Scenario: Finance user requesting budget information")
    print("-" * 50)
    
    # Create finance user session
    finance_session = rbac_engine.create_user_session(
        user_id="finance_001",
        username="Sarah Johnson",
        role=UserRoles.FINANCE_TEAM,
        department="finance"
    )
    
    # Create agents for finance user
    security_agent = agent_factory.create_security_agent(finance_session)
    document_agent = agent_factory.create_document_agent(finance_session)
    specialist_agent = agent_factory.create_role_specialist_agent(finance_session)
    
    # Test workflow
    print("1. 🔒 Security check for budget document access...")
    
    try:
        security_response = security_agent.generate_reply(
            messages=[{"role": "user", "content": "Can I access financial_summary.md?"}]
        )
        print(f"   Security Agent: {security_response}")
    except Exception as e:
        print(f"   Security Agent Error: {str(e)}")
    
    print("\n2. 📄 Document search for budget information...")
    
    try:
        doc_response = document_agent.generate_reply(
            messages=[{"role": "user", "content": "Find documents about budget and financial performance"}]
        )
        print(f"   Document Agent: {doc_response}")
    except Exception as e:
        print(f"   Document Agent Error: {str(e)}")
    
    print("\n3. 🎯 Financial specialist analysis...")
    
    try:
        specialist_response = specialist_agent.generate_reply(
            messages=[{"role": "user", "content": "Analyze Q4 financial performance"}]
        )
        print(f"   Specialist Agent: {specialist_response}")
    except Exception as e:
        print(f"   Specialist Agent Error: {str(e)}")
    
    # Test unauthorized access scenario
    print("\n📋 Scenario: General employee trying to access HR documents")
    print("-" * 50)
    
    # Create general employee session
    general_session = rbac_engine.create_user_session(
        user_id="general_001",
        username="John Doe",
        role=UserRoles.GENERAL_EMPLOYEES,
        department="general"
    )
    
    # Create security agent for general employee
    general_security_agent = agent_factory.create_security_agent(general_session)
    
    print("1. 🔒 Attempting to access restricted HR document...")
    
    try:
        unauthorized_response = general_security_agent.generate_reply(
            messages=[{"role": "user", "content": "Can I access employee_handbook.md?"}]
        )
        print(f"   Security Agent: {unauthorized_response}")
    except Exception as e:
        print(f"   Security Agent Error: {str(e)}")
    
    # Get system statistics
    stats = rbac_engine.get_role_statistics()
    print(f"\n📊 System Statistics:")
    print(f"   Active sessions: {stats['total_sessions']}")
    print(f"   Role distribution: {stats['role_distribution']}")
    print(f"   Query usage: {stats['query_usage']}")
    
    return rbac_engine, agent_factory

# ===============================
# 5. MAIN EXECUTION & TESTING
# ===============================

async def main():
    """Main execution function for RBAC testing"""
    print("🚀 Starting RBAC Logic Testing Pipeline...")
    
    # Initialize RBAC components
    rbac_engine = RBACEngine()
    
    # Initialize ModelClient if available
    model_client = None
    if ModelClient:
        try:
            model_client = ModelClient()
            print("✅ ModelClient ready for AutoGen agents")
        except Exception as e:
            print(f"⚠️  ModelClient initialization failed: {str(e)}")
    
    # Initialize agent factory
    agent_factory = RBACAgentFactory(rbac_engine, model_client)
    
    # Initialize test suite
    test_suite = RBACTestSuite(rbac_engine, agent_factory)
    
    # Run comprehensive tests
    print("\n🧪 Running RBAC Test Suite...")
    
    # Test 1: Role access permissions
    access_results = await test_suite.test_role_access_permissions()
    
    # Test 2: AutoGen agent security
    agent_results = await test_suite.test_autogen_agent_security()
    
    # Test 3: Query rate limiting
    rate_limit_results = await test_suite.test_query_rate_limiting()
    
    # Test 4: Session management
    session_results = await test_suite.test_session_management()
    
    # Test 5: Full workflow integration
    await test_full_rbac_workflow()
    
    # Compile final results
    final_results = {
        "access_permissions": access_results,
        "agent_security": agent_results,
        "rate_limiting": rate_limit_results,
        "session_management": session_results
    }
    
    # Print summary
    print("\n" + "="*70)
    print("📊 RBAC TESTING SUMMARY")
    print("="*70)
    
    total_tests = sum([
        access_results["total_tests"],
        agent_results["total_tests"], 
        rate_limit_results["total_tests"],
        session_results["total_tests"]
    ])
    
    total_passed = sum([
        access_results["passed_tests"],
        agent_results["passed_tests"],
        rate_limit_results["passed_tests"],
        session_results["passed_tests"]
    ])
    
    total_failed = sum([
        access_results["failed_tests"],
        agent_results["failed_tests"],
        rate_limit_results["failed_tests"],
        session_results["failed_tests"]
    ])
    
    success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"📋 Total Tests: {total_tests}")
    print(f"✅ Passed: {total_passed}")
    print(f"❌ Failed: {total_failed}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    print(f"\n📊 Test Breakdown:")
    print(f"   🔐 Access Permissions: {access_results['passed_tests']}/{access_results['total_tests']}")
    print(f"   🤖 Agent Security: {agent_results['passed_tests']}/{agent_results['total_tests']}")
    print(f"   ⏱️  Rate Limiting: {rate_limit_results['passed_tests']}/{rate_limit_results['total_tests']}")
    print(f"   👥 Session Management: {session_results['passed_tests']}/{session_results['total_tests']}")
    
    if success_rate >= 90:
        print("\n🎉 RBAC system is working excellently!")
    elif success_rate >= 75:
        print("\n👍 RBAC system is working well with minor issues")
    else:
        print("\n⚠️  RBAC system needs attention - several tests failed")
    
    print("\n✅ RBAC Logic Testing completed!")
    print("🎯 Ready for next phase: Individual Agents (Notebook 05)")
    
    return rbac_engine, agent_factory, final_results

# Run the main execution
if __name__ == "__main__":
    rbac_engine, agent_factory, test_results = asyncio.run(main())

# ===============================
# 6. INTERACTIVE TESTING SECTION
# ===============================

print("\n" + "="*70)
print("🧪 INTERACTIVE RBAC TESTING")
print("="*70)

# Interactive testing functions
if 'rbac_engine' in locals() and 'agent_factory' in locals():
    
    def create_test_user(role_name: str, username: str = None):
        """Create a test user session for interactive testing"""
        role = UserRoles(role_name)
        user_name = username or f"Test User ({role_name})"
        
        session = rbac_engine.create_user_session(
            user_id=f"interactive_{role_name}",
            username=user_name,
            role=role,
            department=role_name.split('_')[0]
        )
        
        print(f"✅ Created test session for {user_name}")
        print(f"   Role: {role.value}")
        print(f"   Accessible documents: {len(session.accessible_documents)}")
        print(f"   Query limit: {session.max_queries}")
        
        return session
    
    def test_document_access(session: UserSession, document_name: str):
        """Test if a user can access a specific document"""
        can_access = rbac_engine.validate_document_access(session, document_name)
        
        if can_access:
            print(f"✅ {session.username} CAN access {document_name}")
        else:
            print(f"❌ {session.username} CANNOT access {document_name}")
        
        return can_access
    
    def create_agent_for_session(session: UserSession, agent_type: str = "document"):
        """Create an AutoGen agent for a session"""
        if agent_type == "security":
            agent = agent_factory.create_security_agent(session)
        elif agent_type == "document":
            agent = agent_factory.create_document_agent(session)
        elif agent_type == "specialist":
            agent = agent_factory.create_role_specialist_agent(session)
        else:
            print(f"❌ Unknown agent type: {agent_type}")
            return None
        
        print(f"✅ Created {agent_type} agent for {session.username}")
        return agent
    
    # Example interactive tests
    print("\n🔬 Example Interactive Tests:")
    print("-" * 40)
    
    # Create test sessions for different roles
    finance_session = create_test_user("finance_team", "Alice (Finance)")
    marketing_session = create_test_user("marketing_team", "Bob (Marketing)")
    general_session = create_test_user("general_employees", "Charlie (General)")
    
    # Test document access
    print(f"\n📄 Testing document access:")
    test_document_access(finance_session, "financial_summary.md")
    test_document_access(marketing_session, "financial_summary.md")
    test_document_access(general_session, "financial_summary.md")
    
    # Create and test agents
    print(f"\n🤖 Creating AutoGen agents:")
    finance_doc_agent = create_agent_for_session(finance_session, "document")
    general_security_agent = create_agent_for_session(general_session, "security")
    
    print(f"\n💡 Interactive testing setup complete!")
    print(f"   Use the created sessions and agents for further testing")
    print(f"   Available functions: create_test_user(), test_document_access(), create_agent_for_session()")

print("\n🎉 Notebook 04 completed successfully!")
print("📝 Next steps:")
print("  1. ✅ Model Client Testing (Notebook 01) - COMPLETED")
print("  2. ✅ Document Processing (Notebook 02) - COMPLETED")
print("  3. ✅ Vector Store Setup (Notebook 03) - COMPLETED")
print("  4. ✅ RBAC Logic Testing (Notebook 04) - COMPLETED")
print("  5. ⏳ Individual Agents (Notebook 05) - NEXT")
print("\n🚀 RBAC System Status:")
print("  ✅ Role-based access control implemented")
print("  ✅ AutoGen 0.4 agents with security integration")
print("  ✅ Session management and authentication")
print("  ✅ Query rate limiting and quota enforcement")
print("  ✅ Comprehensive testing framework")
print("  🎯 Ready for individual agent creation and testing!")