Requirement already satisfied: pyautogen in c:\euron\autogen-finserve\fin_env\lib\site-packages (0.9.0)
Requirement already satisfied: autogen-agentchat in c:\euron\autogen-finserve\fin_env\lib\site-packages (0.6.2)
Requirement already satisfied: autogen-core in c:\euron\autogen-finserve\fin_env\lib\site-packages (0.6.2)
Requirement already satisfied: anyio<5.0.0,>=3.0.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (4.9.0)
Requirement already satisfied: asyncer==0.0.8 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (0.0.8)
Requirement already satisfied: diskcache in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (5.6.3)
Requirement already satisfied: docker in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (7.1.0)
Requirement already satisfied: httpx<1,>=0.28.1 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (0.28.1)
Requirement already satisfied: packaging in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (24.2)
Requirement already satisfied: pydantic<3,>=2.6.1 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (2.11.7)
Requirement already satisfied: python-dotenv in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (1.1.1)
Requirement already satisfied: termcolor in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (3.1.0)
Requirement already satisfied: tiktoken in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pyautogen) (0.9.0)
Requirement already satisfied: idna>=2.8 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (3.10)
Requirement already satisfied: sniffio>=1.1 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (1.3.1)
Requirement already satisfied: typing_extensions>=4.5 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from anyio<5.0.0,>=3.0.0->pyautogen) (4.14.0)
Requirement already satisfied: certifi in c:\euron\autogen-finserve\fin_env\lib\site-packages (from httpx<1,>=0.28.1->pyautogen) (2025.6.15)
Requirement already satisfied: httpcore==1.* in c:\euron\autogen-finserve\fin_env\lib\site-packages (from httpx<1,>=0.28.1->pyautogen) (1.0.9)
Requirement already satisfied: h11>=0.16 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from httpcore==1.*->httpx<1,>=0.28.1->pyautogen) (0.16.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pydantic<3,>=2.6.1->pyautogen) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pydantic<3,>=2.6.1->pyautogen) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from pydantic<3,>=2.6.1->pyautogen) (0.4.1)
Requirement already satisfied: jsonref~=1.1.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from autogen-core) (1.1.0)
Requirement already satisfied: opentelemetry-api>=1.34.1 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from autogen-core) (1.34.1)
Requirement already satisfied: opentelemetry-semantic-conventions==0.55b1 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from autogen-core) (0.55b1)
Requirement already satisfied: pillow>=11.0.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from autogen-core) (11.3.0)
Requirement already satisfied: protobuf~=5.29.3 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from autogen-core) (5.29.5)
Requirement already satisfied: importlib-metadata<8.8.0,>=6.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from opentelemetry-api>=1.34.1->autogen-core) (8.7.0)
Requirement already satisfied: zipp>=3.20 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.34.1->autogen-core) (3.23.0)
Requirement already satisfied: pywin32>=304 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from docker->pyautogen) (310)
Requirement already satisfied: requests>=2.26.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from docker->pyautogen) (2.32.4)
Requirement already satisfied: urllib3>=1.26.0 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from docker->pyautogen) (2.5.0)
Requirement already satisfied: charset_normalizer<4,>=2 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from requests>=2.26.0->docker->pyautogen) (3.4.2)
Requirement already satisfied: regex>=2022.1.18 in c:\euron\autogen-finserve\fin_env\lib\site-packages (from tiktoken->pyautogen) (2024.11.6)
