# Copyright (c) 2023 - 2025, AG2ai, Inc., AG2ai open-source projects maintainers and core contributors
#
# SPDX-License-Identifier: Apache-2.0
def fraction_to_mixed_numbers(numerator, denominator):
    """Simplifies a fraction to its lowest terms and returns it as a mixed number.

    Args:
        numerator (int): The numerator of the fraction.
        denominator (int): The denominator of the fraction.

    Returns:
        str: The simplified fraction as a string. If the fraction is already an integer, it returns the integer as a string.
             If the fraction is a proper fraction, it returns the mixed number representation as a string.
             If the numerator or denominator is not an integer, it returns an error message.
             If the denominator is zero, it returns an error message.
    """
    from sympy import Rational

    # Ensure that numerator and denominator are integers
    if not isinstance(numerator, int) or not isinstance(denominator, int):
        return "Error: Numerator and denominator must be integers."

    # Handle the case where the denominator is zero
    if denominator == 0:
        return "Error: Denominator cannot be zero."

    # Simplify the fraction to its lowest terms
    result = Rational(numerator, denominator)
    # Return the result as a mixed number if needed
    if result.is_integer:
        return str(int(result))
    else:
        # Result as a mixed number
        integer_part = int(result)
        fractional_part = result - integer_part
        if fractional_part != 0:
            return f"{integer_part} {fractional_part}"
        else:
            return str(integer_part)
